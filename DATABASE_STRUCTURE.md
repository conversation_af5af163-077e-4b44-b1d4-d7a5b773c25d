# هيكل قاعدة البيانات - TIaF Report System

## نظرة عامة

نظام TIaF Report System يستخدم قاعدة بيانات MySQL مع هيكل محدد لتخزين بيانات التقارير الضريبية. الهيكل مصمم لتسهيل الاستيراد المستمر للبيانات من ملفات CSV.

## الجداول الأساسية (بدون مفتاح أساسي)

### خصائص مهمة:
- **لا تحتوي على مفتاح أساسي `id`**
- **البيانات منظمة حسب `export_date`**
- **تاريخ التصدير مستخرج من اسم الملف**
- **تنسيق اسم الملف**: `TableNameEXPORT_YYYYMMDD.csv`

### 1. جدول طلبات المحاسبة (accounting)

```sql
CREATE TABLE `accounting` (
    `request_number` varchar(50) COMMENT 'رقم الطلب',
    `creation_date` datetime COMMENT 'تاريخ الإنشاء',
    `created_by` varchar(100) COMMENT 'منشئ الطلب',
    `modification_date` datetime COMMENT 'تاريخ التعديل',
    `modified_by` varchar(100) COMMENT 'معدل الطلب',
    `request_status` varchar(10) COMMENT 'حالة الطلب',
    `status_description` varchar(255) COMMENT 'وصف الحالة',
    `request_type` varchar(10) COMMENT 'نوع الطلب',
    `request_type_name` varchar(255) COMMENT 'اسم نوع الطلب',
    `tax_registration_number` varchar(50) COMMENT 'رقم التسجيل الضريبي',
    `office_code` varchar(10) COMMENT 'كود المأمورية',
    `office_name` varchar(255) COMMENT 'اسم المأمورية',
    `taxpayer_name` varchar(255) COMMENT 'اسم المكلف',
    `address` text COMMENT 'العنوان',
    `phone_number` varchar(50) COMMENT 'رقم الهاتف',
    `email` varchar(255) COMMENT 'البريد الإلكتروني',
    `accounting_type` varchar(10) COMMENT 'نوع المحاسبة',
    `accounting_type_description` varchar(255) COMMENT 'وصف نوع المحاسبة',
    `container` varchar(10) COMMENT 'الوعاء',
    `container_name` varchar(255) COMMENT 'اسم الوعاء',
    `period` varchar(20) COMMENT 'الفترة',
    `dispute_stage` varchar(10) COMMENT 'مرحلة النزاع',
    `dispute_stage_description` varchar(255) COMMENT 'وصف مرحلة النزاع',
    `case_number` varchar(100) COMMENT 'رقم القضية',
    `dispute_authority` varchar(10) COMMENT 'جهة النزاع',
    `dispute_authority_name` varchar(255) COMMENT 'اسم جهة النزاع',
    `other_authority` varchar(255) COMMENT 'جهة أخرى',
    `tax_according_to_declaration` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار',
    `tax_from_last_assessment` decimal(15,2) COMMENT 'الضريبة من آخر ربط',
    `last_assessment_year` varchar(10) COMMENT 'سنة آخر ربط',
    `tax_according_to_form` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج',
    `tax_paid` decimal(15,2) COMMENT 'الضريبة المسددة',
    `tax_according_to_declaration_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للإقرار قبل التعديل',
    `tax_from_last_assessment_before_modification` decimal(15,2) COMMENT 'الضريبة من آخر ربط قبل التعديل',
    `tax_according_to_form_before_modification` decimal(15,2) COMMENT 'الضريبة طبقاً للنموذج قبل التعديل',
    `tax_paid_before_modification` decimal(15,2) COMMENT 'الضريبة المسددة قبل التعديل',
    `expected_tax` decimal(15,2) COMMENT 'الضريبة المتوقعة',
    `tax_according_to_law` decimal(15,2) COMMENT 'الضريبة طبقاً للقانون',
    `tax_due_payment` decimal(15,2) COMMENT 'الضريبة المستحقة',
    `export_date` date NOT NULL COMMENT 'تاريخ التصدير المستخرج من اسم الملف',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإدراج في النظام',
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ آخر تحديث',
    
    KEY `idx_export_date` (`export_date`),
    KEY `idx_request_number` (`request_number`),
    KEY `idx_office_code` (`office_code`),
    KEY `idx_taxpayer_name` (`taxpayer_name`),
    KEY `idx_request_status` (`request_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**مثال اسم الملف**: `AccountingEXPORT_20250526.csv`

### 2. جدول طلبات إنهاء النزاع (conflict)

نفس هيكل جدول المحاسبة مع نفس الحقول والفهارس.

**مثال اسم الملف**: `ConflictEXPORT_20250526.csv`

### 3. جدول طلبات تسوية النزاع (dispute)

نفس هيكل جدول المحاسبة مع نفس الحقول والفهارس.

**مثال اسم الملف**: `DisputeEXPORT_20250526.csv`

## الجداول المساعدة (مع مفتاح أساسي)

### 1. جدول التصنيف (classification)

```sql
CREATE TABLE `classification` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `classification_type` varchar(50) NOT NULL COMMENT 'التصنيف',
    `office_code` varchar(10) NOT NULL COMMENT 'كود المأمورية',
    `office_name` varchar(255) NOT NULL COMMENT 'اسم المأمورية',
    `region` varchar(100) NOT NULL COMMENT 'المنطقة',
    `office_branch` varchar(255) NOT NULL COMMENT 'المامورية',
    `specialization` varchar(100) NOT NULL COMMENT 'الاختصاص',
    `general_manager` varchar(100) NOT NULL COMMENT 'مديري العموم',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_office_code` (`office_code`),
    KEY `idx_classification_type` (`classification_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**مصدر البيانات**: `Mapping Table/جدول التصنيف.csv`

### 2. جدول الوعاء (container)

```sql
CREATE TABLE `container` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `container_code` varchar(10) NOT NULL COMMENT 'الوعاء',
    `container_name` varchar(255) NOT NULL COMMENT 'اسم الوعاء',
    `classification` varchar(50) NOT NULL COMMENT 'التصنيف',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_container_code` (`container_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**مصدر البيانات**: `Mapping Table/جدول الوعاء.csv`

### 3. جدول حالة الطلب (request_status)

```sql
CREATE TABLE `request_status` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `status_code` varchar(10) NOT NULL COMMENT 'حالة الطلب',
    `status_description` varchar(255) NOT NULL COMMENT 'وصف الحالة',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_status_code` (`status_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**مصدر البيانات**: `Mapping Table/جدول حالة الطلب.csv`

### 4. جدول إعدادات النظام (system_settings)

```sql
CREATE TABLE `system_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL,
    `setting_value` text,
    `description` varchar(255),
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## آلية استخراج تاريخ التصدير

### تنسيق اسم الملف:
```
TableNameEXPORT_YYYYMMDD.csv
```

### أمثلة:
- `AccountingEXPORT_20250526.csv` → `export_date = 2025-05-26`
- `ConflictEXPORT_20241215.csv` → `export_date = 2024-12-15`
- `DisputeEXPORT_20250101.csv` → `export_date = 2025-01-01`

### كود استخراج التاريخ:
```php
function extractExportDate($filename) {
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);
        
        if (checkdate($month, $day, $year)) {
            return "$year-$month-$day";
        }
    }
    
    return date('Y-m-d'); // التاريخ الحالي كافتراضي
}
```

## الفهارس والأداء

### الفهارس الأساسية:
- `idx_export_date` - للبحث حسب تاريخ التصدير
- `idx_request_number` - للبحث حسب رقم الطلب
- `idx_office_code` - للبحث حسب كود المأمورية
- `idx_taxpayer_name` - للبحث حسب اسم المكلف
- `idx_request_status` - للبحث حسب حالة الطلب

### تحسينات الأداء:
- عدم وجود مفتاح أساسي يسرع عملية الإدراج
- الفهارس محسنة للاستعلامات الشائعة
- استخدام InnoDB لدعم المعاملات

## إرشادات الاستيراد

### 1. تحضير الملفات:
- تأكد من تنسيق اسم الملف الصحيح
- تحقق من صحة التاريخ في اسم الملف
- تأكد من تنسيق CSV صحيح

### 2. عملية الاستيراد:
- يتم حذف البيانات السابقة لنفس التاريخ تلقائياً
- أو يمكن حذف جميع البيانات (خيار اختياري)
- يتم إضافة `export_date` تلقائياً من اسم الملف

### 3. التحقق من النتائج:
- مراجعة عدد السجلات المستوردة
- التحقق من وجود أخطاء
- مراجعة تاريخ التصدير المستخرج

## الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي:
```bash
mysqldump -u root -p tiaf_db > backup_$(date +%Y%m%d).sql
```

### تحسين الجداول:
```sql
OPTIMIZE TABLE accounting, conflict, dispute;
```

### إحصائيات الجداول:
```sql
SELECT 
    table_name,
    table_rows,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)'
FROM information_schema.tables 
WHERE table_schema = 'tiaf_db';
```

---

**ملاحظة**: هذا الهيكل مصمم خصيصاً لمتطلبات نظام TIaF Report System ويجب الالتزام بتنسيق أسماء الملفات المحدد لضمان عمل النظام بشكل صحيح.
