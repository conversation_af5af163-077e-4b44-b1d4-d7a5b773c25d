<?php
/**
 * ملف إعداد قاعدة البيانات - الجزء الثالث
 * إنشاء جدول طلبات تسوية النزاع (Dispute)
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بقاعدة البيانات
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>🚀 إنشاء جدول طلبات تسوية النزاع - TIaF Report System</h2>";
    
    // ===== إنشاء جدول طلبات تسوية النزاع (Dispute) =====
    $sql_dispute = "
    CREATE TABLE IF NOT EXISTS `dispute` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `request_number` varchar(50) NOT NULL,
        `creation_date` datetime,
        `created_by` varchar(100),
        `modification_date` datetime,
        `modified_by` varchar(100),
        `request_status` varchar(10),
        `status_description` varchar(255),
        `request_type` varchar(10),
        `request_type_name` varchar(255),
        `tax_registration_number` varchar(50),
        `office_code` varchar(10),
        `office_name` varchar(255),
        `taxpayer_name` varchar(255),
        `address` text,
        `phone_number` varchar(50),
        `email` varchar(255),
        `accounting_type` varchar(10),
        `accounting_type_description` varchar(255),
        `container` varchar(10),
        `container_description` varchar(255),
        `period` varchar(20),
        `dispute_stage` varchar(10),
        `dispute_stage_name` varchar(255),
        `case_number` varchar(100),
        `dispute_authority` varchar(10),
        `dispute_authority_name` varchar(255),
        `other_authority` varchar(255),
        `tax_according_to_declaration` decimal(15,2),
        `tax_from_last_assessment` decimal(15,2),
        `last_assessment_year` varchar(10),
        `tax_according_to_form` decimal(15,2),
        `tax_paid` decimal(15,2),
        `tax_according_to_declaration_before_modification` decimal(15,2),
        `tax_from_last_assessment_before_modification` decimal(15,2),
        `tax_according_to_form_before_modification` decimal(15,2),
        `tax_paid_before_modification` decimal(15,2),
        `tax_according_to_law` decimal(15,2),
        `expected_tax` decimal(15,2),
        `tax_due_payment` decimal(15,2),
        `export_date` date NOT NULL,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_request_number` (`request_number`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_export_date` (`export_date`),
        KEY `idx_tax_registration_number` (`tax_registration_number`),
        KEY `idx_request_status` (`request_status`),
        KEY `idx_container` (`container`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_dispute);
    echo "<p>✅ تم إنشاء جدول طلبات تسوية النزاع (dispute)</p>";
    
    // إنشاء فهارس إضافية لتحسين الأداء
    echo "<h3>إنشاء الفهارس الإضافية...</h3>";
    
    // فهارس للجداول الثلاثة
    $tables = ['accounting', 'conflict', 'dispute'];
    
    foreach ($tables as $table) {
        try {
            // فهرس مركب للبحث السريع
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_{$table}_search ON `{$table}` (office_code, export_date, request_status)");
            echo "<p>✅ تم إنشاء فهرس البحث لجدول {$table}</p>";
            
            // فهرس للتاريخ والحالة
            $pdo->exec("CREATE INDEX IF NOT EXISTS idx_{$table}_date_status ON `{$table}` (export_date, request_status)");
            echo "<p>✅ تم إنشاء فهرس التاريخ والحالة لجدول {$table}</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>⚠️ تحذير: فهرس موجود بالفعل لجدول {$table}</p>";
        }
    }
    
    // إنشاء جدول إعدادات النظام
    $sql_settings = "
    CREATE TABLE IF NOT EXISTS `system_settings` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `setting_key` varchar(100) NOT NULL,
        `setting_value` text,
        `description` varchar(255),
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `setting_key` (`setting_key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_settings);
    echo "<p>✅ تم إنشاء جدول إعدادات النظام (system_settings)</p>";
    
    // إدخال إعدادات افتراضية
    $default_settings = [
        ['app_name', 'TIaF Report System', 'اسم النظام'],
        ['app_version', '1.0', 'إصدار النظام'],
        ['default_language', 'ar', 'اللغة الافتراضية'],
        ['records_per_page', '50', 'عدد السجلات في الصفحة'],
        ['date_format', 'd-m-Y', 'تنسيق التاريخ'],
        ['timezone', 'Africa/Cairo', 'المنطقة الزمنية']
    ];
    
    $stmt = $pdo->prepare("INSERT IGNORE INTO system_settings (setting_key, setting_value, description) VALUES (?, ?, ?)");
    foreach ($default_settings as $setting) {
        $stmt->execute($setting);
    }
    echo "<p>✅ تم إدخال الإعدادات الافتراضية</p>";
    
    echo "<h3>🎉 تم إكمال إعداد قاعدة البيانات بنجاح!</h3>";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>ملخص ما تم إنشاؤه:</h4>";
    echo "<ul>";
    echo "<li>✅ قاعدة البيانات: tiaf_db</li>";
    echo "<li>✅ الجداول المرجعية: classification, container, request_status</li>";
    echo "<li>✅ الجداول الأساسية: accounting, conflict, dispute</li>";
    echo "<li>✅ جدول الإعدادات: system_settings</li>";
    echo "<li>✅ الفهارس والمفاتيح للبحث السريع</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><a href='import_data.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>انتقل لصفحة استيراد البيانات</a></p>";
    echo "<p><a href='index.php'>العودة للصفحة الرئيسية</a></p>";
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
}
?>
