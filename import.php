<?php
set_time_limit(0); // السماح بوقت تنفيذ غير محدود
$pageTitle = 'استيراد ملفات البيانات';
include 'header.php';
?>
<div class="import-container">
    <h1>استيراد ملفات البيانات (CSV)</h1>
    <form id="importForm" action="import.php" method="post" enctype="multipart/form-data" autocomplete="off">
        <label for="table">اختر نوع الجدول:</label>
        <select name="table" id="table" required>
            <option value="accounting">طلبات المحاسبة (Accounting)</option>
            <option value="dispute">طلبات تسوية النزاع (Dispute)</option>
            <option value="conflict">طلبات إنهاء النزاع (Conflict)</option>
        </select>
        <label for="csvfile">اختر ملف CSV:</label>
        <input type="file" name="csvfile" id="csvfile" accept=".csv" required>
        <button type="submit">استيراد الملف</button>
    </form>
    <div id="loadingMsg" style="display:none; color:#7c4dff; font-weight:bold; margin:18px 0;">جاري تحميل واستيراد البيانات... يرجى الانتظار</div>
    <div id="resultMsg"></div>
    <?php
    // معالجة الاستيراد فقط إذا كان الطلب AJAX (fetch)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csvfile']) && isset($_POST['table'])) {
        flush();
        ob_flush();
        $table = $_POST['table'];
        $file = $_FILES['csvfile']['tmp_name'];
        $filename = $_FILES['csvfile']['name'];
        $exportDate = '';
        if (preg_match('/(\d{8})/', $filename, $m)) {
            $exportDate = $m[1];
            $exportDate = substr($exportDate,6,2).'-'.substr($exportDate,4,2).'-'.substr($exportDate,0,4);
        }
        $handle = fopen($file, 'r');
        if ($handle) {
            require_once 'db_connect.php';
            $header = fgetcsv($handle);
            $header = array_map(function($h) {
                $h = preg_replace('/^[\xEF\xBB\xBF]+|[\x00-\x1F\x7F\xA0]/u', '', $h);
                return trim($h);
            }, $header);
            $result = $conn->query("SHOW COLUMNS FROM `$table`");
            $dbCols = [];
            while ($row = $result->fetch_assoc()) {
                $dbCols[] = $row['Field'];
            }
            $colMap = [];
            foreach ($header as $i => $h) {
                foreach ($dbCols as $dbCol) {
                    if ($h === $dbCol) {
                        $colMap[$i] = $dbCol;
                        break;
                    }
                }
            }
            $colMap[count($header)] = 'Export_Date';
            $rowCount = 0;
            $errorMsg = '';
            while (($data = fgetcsv($handle)) !== false) {
                $data[] = $exportDate;
                $values = [];
                $cols = [];
                foreach ($colMap as $i => $colName) {
                    if (isset($data[$i])) {
                        $cols[] = "`$colName`";
                        $values[] = "'".$conn->real_escape_string($data[$i])."'";
                    }
                }
                if (count($cols) > 0) {
                    $sql = "INSERT INTO `$table` (".implode(',', $cols).") VALUES (".implode(',', $values).")";
                    if (!$conn->query($sql)) {
                        $errorMsg = $conn->error;
                        break;
                    }
                    $rowCount++;
                }
            }
            fclose($handle);
            if ($errorMsg) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'حدث خطأ أثناء الاستيراد: ' . $errorMsg
                ]);
                exit;
            }
            // استجابة JSON لنجاح العملية
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => "تم استيراد $rowCount صف بنجاح إلى جدول ".htmlspecialchars($table)
            ]);
            exit;
        } else {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'فشل في قراءة الملف'
            ]);
            exit;
        }
    }
    ?>
</div>
<?php include 'footer.php'; ?>
