<?php
/**
 * الدوال المساعدة للنظام
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

require_once dirname(__DIR__) . '/config/database.php';

/**
 * دالة لاستخراج التاريخ من اسم الملف
 */
function extractDateFromFilename($filename) {
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);
        return "$year-$month-$day";
    }
    return date('Y-m-d');
}

/**
 * دالة لتنظيف البيانات المستوردة
 */
function cleanImportData($data) {
    $data = str_replace(',', '', $data);
    $data = trim($data);
    return $data;
}

/**
 * دالة لتحويل التاريخ من تنسيق DD.MM.YYYY إلى YYYY-MM-DD
 */
function convertDateTime($dateTime) {
    if (empty($dateTime)) return null;
    
    if (preg_match('/(\d{2})\.(\d{2})\.(\d{4})\s+(\d{2}:\d{2}:\d{2})/', $dateTime, $matches)) {
        return $matches[3] . '-' . $matches[2] . '-' . $matches[1] . ' ' . $matches[4];
    }
    
    return $dateTime;
}

/**
 * دالة للحصول على إحصائيات الجدول
 */
function getTableStats($tableName) {
    try {
        $pdo = getDBConnection();
        
        $stats = [];
        
        // إجمالي السجلات
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM `$tableName`");
        $stats['total'] = $stmt->fetchColumn();
        
        // السجلات حسب الحالة
        $stmt = $pdo->query("
            SELECT request_status, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY request_status 
            ORDER BY count DESC
        ");
        $stats['by_status'] = $stmt->fetchAll();
        
        // السجلات حسب المأمورية (أكثر 10)
        $stmt = $pdo->query("
            SELECT office_code, office_name, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY office_code, office_name 
            ORDER BY count DESC 
            LIMIT 10
        ");
        $stats['by_office'] = $stmt->fetchAll();
        
        // السجلات حسب تاريخ التصدير
        $stmt = $pdo->query("
            SELECT export_date, COUNT(*) as count 
            FROM `$tableName` 
            GROUP BY export_date 
            ORDER BY export_date DESC 
            LIMIT 12
        ");
        $stats['by_export_date'] = $stmt->fetchAll();
        
        return $stats;
        
    } catch (Exception $e) {
        error_log("Error getting table stats: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة للحصول على بيانات الرسم البياني
 */
function getChartData($tableName, $type = 'status') {
    try {
        $pdo = getDBConnection();
        
        switch ($type) {
            case 'status':
                $stmt = $pdo->query("
                    SELECT 
                        rs.name as label,
                        COUNT(t.id) as value,
                        rs.code
                    FROM `$tableName` t
                    LEFT JOIN request_status rs ON t.request_status = rs.code
                    GROUP BY t.request_status, rs.name, rs.code
                    ORDER BY value DESC
                ");
                break;
                
            case 'container':
                $stmt = $pdo->query("
                    SELECT 
                        c.name as label,
                        COUNT(t.id) as value,
                        c.code
                    FROM `$tableName` t
                    LEFT JOIN container c ON t.container = c.code
                    GROUP BY t.container, c.name, c.code
                    ORDER BY value DESC
                ");
                break;
                
            case 'monthly':
                $stmt = $pdo->query("
                    SELECT 
                        DATE_FORMAT(export_date, '%Y-%m') as label,
                        COUNT(*) as value
                    FROM `$tableName`
                    GROUP BY DATE_FORMAT(export_date, '%Y-%m')
                    ORDER BY label DESC
                    LIMIT 12
                ");
                break;
                
            default:
                return [];
        }
        
        return $stmt->fetchAll();
        
    } catch (Exception $e) {
        error_log("Error getting chart data: " . $e->getMessage());
        return [];
    }
}

/**
 * دالة للبحث في الجدول
 */
function searchTable($tableName, $filters = [], $page = 1, $limit = 50) {
    try {
        $pdo = getDBConnection();
        
        $where = [];
        $params = [];
        
        // بناء شروط البحث
        if (!empty($filters['request_number'])) {
            $where[] = "request_number LIKE ?";
            $params[] = '%' . $filters['request_number'] . '%';
        }
        
        if (!empty($filters['taxpayer_name'])) {
            $where[] = "taxpayer_name LIKE ?";
            $params[] = '%' . $filters['taxpayer_name'] . '%';
        }
        
        if (!empty($filters['office_code'])) {
            $where[] = "office_code = ?";
            $params[] = $filters['office_code'];
        }
        
        if (!empty($filters['request_status'])) {
            $where[] = "request_status = ?";
            $params[] = $filters['request_status'];
        }
        
        if (!empty($filters['container'])) {
            $where[] = "container = ?";
            $params[] = $filters['container'];
        }
        
        if (!empty($filters['export_date_from'])) {
            $where[] = "export_date >= ?";
            $params[] = $filters['export_date_from'];
        }
        
        if (!empty($filters['export_date_to'])) {
            $where[] = "export_date <= ?";
            $params[] = $filters['export_date_to'];
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        // حساب إجمالي السجلات
        $countSql = "SELECT COUNT(*) FROM `$tableName` $whereClause";
        $stmt = $pdo->prepare($countSql);
        $stmt->execute($params);
        $totalRecords = $stmt->fetchColumn();
        
        // حساب الصفحات
        $totalPages = ceil($totalRecords / $limit);
        $offset = ($page - 1) * $limit;
        
        // جلب البيانات
        $dataSql = "
            SELECT * FROM `$tableName` 
            $whereClause 
            ORDER BY created_at DESC 
            LIMIT $limit OFFSET $offset
        ";
        $stmt = $pdo->prepare($dataSql);
        $stmt->execute($params);
        $records = $stmt->fetchAll();
        
        return [
            'records' => $records,
            'total_records' => $totalRecords,
            'total_pages' => $totalPages,
            'current_page' => $page,
            'limit' => $limit
        ];
        
    } catch (Exception $e) {
        error_log("Error searching table: " . $e->getMessage());
        return [
            'records' => [],
            'total_records' => 0,
            'total_pages' => 0,
            'current_page' => 1,
            'limit' => $limit
        ];
    }
}

/**
 * دالة لتصدير البيانات إلى CSV
 */
function exportToCSV($tableName, $filters = []) {
    try {
        $pdo = getDBConnection();
        
        $where = [];
        $params = [];
        
        // بناء شروط البحث (نفس منطق البحث)
        if (!empty($filters['office_code'])) {
            $where[] = "office_code = ?";
            $params[] = $filters['office_code'];
        }
        
        if (!empty($filters['request_status'])) {
            $where[] = "request_status = ?";
            $params[] = $filters['request_status'];
        }
        
        if (!empty($filters['export_date_from'])) {
            $where[] = "export_date >= ?";
            $params[] = $filters['export_date_from'];
        }
        
        if (!empty($filters['export_date_to'])) {
            $where[] = "export_date <= ?";
            $params[] = $filters['export_date_to'];
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "SELECT * FROM `$tableName` $whereClause ORDER BY created_at DESC";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        $filename = $tableName . '_export_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // إضافة BOM للدعم العربي في Excel
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // كتابة العناوين
        $firstRow = $stmt->fetch();
        if ($firstRow) {
            fputcsv($output, array_keys($firstRow));
            fputcsv($output, $firstRow);
            
            while ($row = $stmt->fetch()) {
                fputcsv($output, $row);
            }
        }
        
        fclose($output);
        exit;
        
    } catch (Exception $e) {
        error_log("Error exporting to CSV: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على قائمة المأموريات من جدول التصنيف
 */
function getOfficesList($tableName = 'accounting') {
    try {
        $pdo = getDBConnection();

        // محاولة الحصول على البيانات من جدول التصنيف أولاً
        $stmt = $pdo->query("
            SELECT DISTINCT office_code, office_name
            FROM classification
            ORDER BY office_name
        ");
        $result = $stmt->fetchAll();

        // إذا لم توجد بيانات في جدول التصنيف، استخدم الجدول الأساسي
        if (empty($result)) {
            $stmt = $pdo->query("
                SELECT DISTINCT office_code, office_name
                FROM `$tableName`
                WHERE office_code IS NOT NULL AND office_name IS NOT NULL
                ORDER BY office_name
            ");
            $result = $stmt->fetchAll();
        }

        return $result;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة للحصول على قائمة أنواع الوعاء من جدول الوعاء
 */
function getContainersList() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("
            SELECT container_code, container_name, classification
            FROM container
            ORDER BY container_code
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة للحصول على قائمة حالات الطلب من جدول حالة الطلب
 */
function getRequestStatusList() {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->query("
            SELECT status_code, status_description
            FROM request_status
            ORDER BY status_code
        ");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        return [];
    }
}

/**
 * دالة لإنشاء رسالة تنبيه
 */
function createAlert($message, $type = 'info') {
    $alertClass = [
        'success' => 'alert-success',
        'error' => 'alert-error',
        'warning' => 'alert-warning',
        'info' => 'alert-info'
    ];
    
    $class = isset($alertClass[$type]) ? $alertClass[$type] : 'alert-info';
    
    return "<div class='alert $class'>$message</div>";
}

/**
 * دالة للتحقق من صحة تنسيق التاريخ
 */
function validateDate($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

/**
 * دالة لحساب النسبة المئوية
 */
function calculatePercentage($part, $total) {
    if ($total == 0) return 0;
    return round(($part / $total) * 100, 2);
}
?>
