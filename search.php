<?php
/**
 * البحث المتقدم - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// معالجة البحث
$search_results = [];
$search_performed = false;
$total_results = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST' || !empty($_GET['search'])) {
    $search_performed = true;
    
    // معاملات البحث
    $search_params = [
        'search_term' => $_POST['search_term'] ?? $_GET['search_term'] ?? '',
        'table_type' => $_POST['table_type'] ?? $_GET['table_type'] ?? 'all',
        'date_from' => $_POST['date_from'] ?? $_GET['date_from'] ?? '',
        'date_to' => $_POST['date_to'] ?? $_GET['date_to'] ?? '',
        'general_manager' => $_POST['general_manager'] ?? $_GET['general_manager'] ?? '',
        'specialization' => $_POST['specialization'] ?? $_GET['specialization'] ?? '',
        'region' => $_POST['region'] ?? $_GET['region'] ?? '',
        'office_branch' => $_POST['office_branch'] ?? $_GET['office_branch'] ?? '',
        'request_status' => $_POST['request_status'] ?? $_GET['request_status'] ?? '',
        'container' => $_POST['container'] ?? $_GET['container'] ?? ''
    ];
    
    try {
        $pdo = getDBConnection();
        
        // تحديد الجداول للبحث فيها
        $tables_to_search = [];
        if ($search_params['table_type'] === 'all') {
            $tables_to_search = ['accounting', 'conflict', 'dispute'];
        } else {
            $tables_to_search = [$search_params['table_type']];
        }
        
        foreach ($tables_to_search as $table) {
            // بناء شروط البحث
            $where_conditions = [];
            $params = [];
            
            // البحث النصي
            if (!empty($search_params['search_term'])) {
                $search_term = '%' . $search_params['search_term'] . '%';
                $where_conditions[] = "(
                    a.request_number LIKE ? OR 
                    a.taxpayer_name LIKE ? OR 
                    a.office_name LIKE ? OR
                    a.email LIKE ? OR
                    a.phone_number LIKE ?
                )";
                $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term, $search_term]);
            }
            
            // فلاتر التاريخ
            if (!empty($search_params['date_from'])) {
                $where_conditions[] = "a.export_date >= ?";
                $params[] = $search_params['date_from'];
            }
            
            if (!empty($search_params['date_to'])) {
                $where_conditions[] = "a.export_date <= ?";
                $params[] = $search_params['date_to'];
            }
            
            // فلاتر التصنيف
            if (!empty($search_params['general_manager'])) {
                $where_conditions[] = "c.general_manager = ?";
                $params[] = $search_params['general_manager'];
            }
            
            if (!empty($search_params['specialization'])) {
                $where_conditions[] = "c.specialization = ?";
                $params[] = $search_params['specialization'];
            }
            
            if (!empty($search_params['region'])) {
                $where_conditions[] = "c.region = ?";
                $params[] = $search_params['region'];
            }
            
            if (!empty($search_params['office_branch'])) {
                $where_conditions[] = "c.office_branch = ?";
                $params[] = $search_params['office_branch'];
            }
            
            if (!empty($search_params['request_status'])) {
                $where_conditions[] = "a.request_status = ?";
                $params[] = $search_params['request_status'];
            }
            
            if (!empty($search_params['container'])) {
                $where_conditions[] = "a.container = ?";
                $params[] = $search_params['container'];
            }
            
            $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
            
            // تنفيذ البحث
            $sql = "
                SELECT 
                    '$table' as table_type,
                    a.request_number,
                    a.taxpayer_name,
                    a.office_name,
                    a.export_date,
                    a.request_status,
                    a.tax_according_to_declaration,
                    a.tax_according_to_law,
                    c.general_manager,
                    c.specialization,
                    c.region,
                    c.office_branch,
                    rs.status_description,
                    cont.container_name
                FROM `$table` a
                LEFT JOIN classification c ON a.office_code = c.office_code
                LEFT JOIN request_status rs ON a.request_status = rs.status_code
                LEFT JOIN container cont ON a.container = cont.container_code
                $where_clause
                ORDER BY a.export_date DESC, a.created_at DESC
                LIMIT 1000
            ";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $results = $stmt->fetchAll();
            
            $search_results = array_merge($search_results, $results);
        }
        
        $total_results = count($search_results);
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// الحصول على قوائم الفلاتر
try {
    $pdo = getDBConnection();
    $managers_list = $pdo->query("SELECT DISTINCT general_manager FROM classification ORDER BY general_manager")->fetchAll();
    $specializations_list = $pdo->query("SELECT DISTINCT specialization FROM classification ORDER BY specialization")->fetchAll();
    $regions_list = $pdo->query("SELECT DISTINCT region FROM classification ORDER BY region")->fetchAll();
    $branches_list = $pdo->query("SELECT DISTINCT office_branch FROM classification ORDER BY office_branch")->fetchAll();
    $status_list = $pdo->query("SELECT * FROM request_status ORDER BY status_code")->fetchAll();
    $containers_list = $pdo->query("SELECT * FROM container ORDER BY container_code")->fetchAll();
} catch (Exception $e) {
    // تجاهل الأخطاء في حالة عدم وجود الجداول
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>البحث المتقدم - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/main.css">
    <style>
        .search-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .search-form {
            background: white;
            color: #333;
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .search-input {
            grid-column: 1 / -1;
        }
        
        .search-input input {
            font-size: 1.1rem;
            padding: 15px;
            border: 2px solid #667eea;
            border-radius: 10px;
        }
        
        .results-section {
            margin: 30px 0;
        }
        
        .result-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
        }
        
        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .result-type {
            background: #667eea;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        
        .result-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .result-field {
            display: flex;
            flex-direction: column;
        }
        
        .result-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 5px;
        }
        
        .result-value {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .no-results {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .search-stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }
        
        .export-section {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="search-section">
            <h1>🔍 البحث المتقدم</h1>
            <p>ابحث في جميع بيانات النظام باستخدام معايير متقدمة ومرنة</p>
        </div>
        
        <div class="search-form">
            <form method="POST" action="">
                <div class="form-grid">
                    <div class="search-input">
                        <label class="form-label">🔍 البحث العام</label>
                        <input type="text" name="search_term" class="form-control" 
                               placeholder="ابحث في رقم الطلب، اسم المكلف، المأمورية، البريد الإلكتروني، أو رقم الهاتف..."
                               value="<?php echo htmlspecialchars($search_params['search_term'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">نوع الجدول</label>
                        <select name="table_type" class="form-control">
                            <option value="all" <?php echo ($search_params['table_type'] ?? '') === 'all' ? 'selected' : ''; ?>>جميع الجداول</option>
                            <option value="accounting" <?php echo ($search_params['table_type'] ?? '') === 'accounting' ? 'selected' : ''; ?>>طلبات المحاسبة</option>
                            <option value="conflict" <?php echo ($search_params['table_type'] ?? '') === 'conflict' ? 'selected' : ''; ?>>طلبات إنهاء النزاع</option>
                            <option value="dispute" <?php echo ($search_params['table_type'] ?? '') === 'dispute' ? 'selected' : ''; ?>>طلبات تسوية النزاع</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">من تاريخ</label>
                        <input type="date" name="date_from" class="form-control" 
                               value="<?php echo htmlspecialchars($search_params['date_from'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="date" name="date_to" class="form-control" 
                               value="<?php echo htmlspecialchars($search_params['date_to'] ?? ''); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">مديري العموم</label>
                        <select name="general_manager" class="form-control">
                            <option value="">جميع مديري العموم</option>
                            <?php if (isset($managers_list)): ?>
                            <?php foreach ($managers_list as $manager): ?>
                            <option value="<?php echo htmlspecialchars($manager['general_manager']); ?>" 
                                    <?php echo ($search_params['general_manager'] ?? '') === $manager['general_manager'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($manager['general_manager']); ?>
                            </option>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">الاختصاص</label>
                        <select name="specialization" class="form-control">
                            <option value="">جميع الاختصاصات</option>
                            <?php if (isset($specializations_list)): ?>
                            <?php foreach ($specializations_list as $spec): ?>
                            <option value="<?php echo htmlspecialchars($spec['specialization']); ?>" 
                                    <?php echo ($search_params['specialization'] ?? '') === $spec['specialization'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($spec['specialization']); ?>
                            </option>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المنطقة</label>
                        <select name="region" class="form-control">
                            <option value="">جميع المناطق</option>
                            <?php if (isset($regions_list)): ?>
                            <?php foreach ($regions_list as $region): ?>
                            <option value="<?php echo htmlspecialchars($region['region']); ?>" 
                                    <?php echo ($search_params['region'] ?? '') === $region['region'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($region['region']); ?>
                            </option>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">المامورية</label>
                        <select name="office_branch" class="form-control">
                            <option value="">جميع المأموريات</option>
                            <?php if (isset($branches_list)): ?>
                            <?php foreach ($branches_list as $branch): ?>
                            <option value="<?php echo htmlspecialchars($branch['office_branch']); ?>" 
                                    <?php echo ($search_params['office_branch'] ?? '') === $branch['office_branch'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch['office_branch']); ?>
                            </option>
                            <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <button type="submit" class="btn btn-primary btn-lg">🔍 بحث متقدم</button>
                    <a href="search.php" class="btn btn-light">🔄 إعادة تعيين</a>
                </div>
            </form>
        </div>
        
        <?php if ($search_performed): ?>
        <div class="results-section">
            <?php if (isset($error_message)): ?>
            <div class="alert alert-danger">
                <h4>❌ خطأ في البحث</h4>
                <p><?php echo $error_message; ?></p>
            </div>
            <?php elseif ($total_results > 0): ?>
            
            <div class="search-stats">
                <h3>📊 نتائج البحث</h3>
                <p>تم العثور على <strong><?php echo number_format($total_results); ?></strong> نتيجة</p>
            </div>
            
            <div class="export-section">
                <a href="?<?php echo http_build_query(array_merge($search_params, ['export' => 'csv', 'search' => '1'])); ?>" class="btn btn-success">📄 تصدير النتائج CSV</a>
            </div>
            
            <?php foreach ($search_results as $result): ?>
            <div class="result-item">
                <div class="result-header">
                    <div class="result-title">
                        <?php echo htmlspecialchars($result['request_number']); ?> - <?php echo htmlspecialchars($result['taxpayer_name']); ?>
                    </div>
                    <div class="result-type">
                        <?php 
                        $type_names = [
                            'accounting' => 'طلبات المحاسبة',
                            'conflict' => 'طلبات إنهاء النزاع',
                            'dispute' => 'طلبات تسوية النزاع'
                        ];
                        echo $type_names[$result['table_type']];
                        ?>
                    </div>
                </div>
                
                <div class="result-details">
                    <div class="result-field">
                        <div class="result-label">تاريخ التصدير</div>
                        <div class="result-value"><?php echo formatDate($result['export_date']); ?></div>
                    </div>
                    <div class="result-field">
                        <div class="result-label">المأمورية</div>
                        <div class="result-value"><?php echo htmlspecialchars($result['office_name']); ?></div>
                    </div>
                    <div class="result-field">
                        <div class="result-label">المنطقة</div>
                        <div class="result-value"><?php echo htmlspecialchars($result['region']); ?></div>
                    </div>
                    <div class="result-field">
                        <div class="result-label">مدير العموم</div>
                        <div class="result-value"><?php echo htmlspecialchars($result['general_manager']); ?></div>
                    </div>
                    <div class="result-field">
                        <div class="result-label">حالة الطلب</div>
                        <div class="result-value"><?php echo htmlspecialchars($result['status_description']); ?></div>
                    </div>
                    <div class="result-field">
                        <div class="result-label">الضريبة المقررة</div>
                        <div class="result-value"><?php echo number_format($result['tax_according_to_declaration'], 2); ?></div>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php else: ?>
            <div class="no-results">
                <h3>🔍 لا توجد نتائج</h3>
                <p>لم يتم العثور على أي نتائج تطابق معايير البحث المحددة</p>
                <p>جرب تعديل معايير البحث أو استخدام كلمات مفتاحية مختلفة</p>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
            <a href="dashboard.php" class="btn btn-info">لوحة التحكم</a>
            <a href="reports/accounting.php" class="btn btn-success">التقارير</a>
        </div>
    </div>
</body>
</html>
