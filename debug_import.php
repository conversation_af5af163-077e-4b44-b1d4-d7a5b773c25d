<?php
/**
 * أداة تشخيص مشاكل الاستيراد - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.4.1
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// دالة لتحويل أسماء الأعمدة العربية إلى أسماء الحقول الإنجليزية
function getColumnMapping() {
    return [
        // الأعمدة الأساسية
        'رقم الطلب' => 'request_number',
        'تاريخ الإنشاء' => 'creation_date',
        'منشئ الطلب' => 'created_by',
        'تاريخ التعديل' => 'modification_date',
        'معدل الطلب' => 'modified_by',
        'حالة الطلب' => 'request_status',
        'وصف الحالة' => 'status_description',
        'نوع الطلب' => 'request_type',
        'اسم نوع الطلب' => 'request_type_name',
        'رقم التسجيل الضريبي' => 'tax_registration_number',
        'كود المأمورية' => 'office_code',
        'اسم المأمورية' => 'office_name',
        'اسم المكلف' => 'taxpayer_name',
        'العنوان' => 'address',
        'رقم الهاتف' => 'phone_number',
        'البريد الإلكتروني' => 'email',
        'نوع المحاسبة' => 'accounting_type',
        'وصف نوع المحاسبة' => 'accounting_type_description',
        'الوعاء' => 'container',
        'اسم الوعاء' => 'container_name',
        'الفترة' => 'period',
        'مرحلة النزاع' => 'dispute_stage',
        'وصف مرحلة النزاع' => 'dispute_stage_description',
        'رقم القضية' => 'case_number',
        'جهة النزاع' => 'dispute_authority',
        'اسم جهة النزاع' => 'dispute_authority_name',
        'جهة أخرى' => 'other_authority',
        'الضريبة طبقاً للإقرار' => 'tax_according_to_declaration',
        'الضريبة من آخر ربط' => 'tax_from_last_assessment',
        'سنة آخر ربط' => 'last_assessment_year',
        'الضريبة طبقاً للنموذج' => 'tax_according_to_form',
        'الضريبة المسددة' => 'tax_paid',
        'الضريبة المتوقعة' => 'expected_tax',
        'الضريبة طبقاً للقانون' => 'tax_according_to_law',
        'الضريبة المستحقة' => 'tax_due_payment'
    ];
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشاكل الاستيراد - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #667eea;
        }
        
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
        
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        
        .warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 0.9rem;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            overflow-x: auto;
        }
        
        .btn {
            background: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص مشاكل الاستيراد</h1>
            <p>أداة لتشخيص وحل مشاكل استيراد ملفات CSV</p>
        </div>
        
        <div class="content">
            <?php
            try {
                $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                echo "<div class='section success'>";
                echo "<h3>✅ الاتصال بقاعدة البيانات</h3>";
                echo "<p>تم الاتصال بقاعدة البيانات بنجاح</p>";
                echo "</div>";
                
                // عرض هيكل الجداول
                $tables = ['accounting', 'conflict', 'dispute'];
                
                foreach ($tables as $table) {
                    echo "<div class='section'>";
                    echo "<h3>🗄️ هيكل جدول $table</h3>";
                    
                    try {
                        $stmt = $pdo->query("DESCRIBE `$table`");
                        $columns = $stmt->fetchAll();
                        
                        echo "<table>";
                        echo "<tr><th>اسم الحقل</th><th>النوع</th><th>يقبل NULL</th><th>المفتاح</th><th>القيمة الافتراضية</th></tr>";
                        
                        foreach ($columns as $column) {
                            echo "<tr>";
                            echo "<td>{$column['Field']}</td>";
                            echo "<td>{$column['Type']}</td>";
                            echo "<td>{$column['Null']}</td>";
                            echo "<td>{$column['Key']}</td>";
                            echo "<td>{$column['Default']}</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                        echo "<p>📊 إجمالي الحقول: " . count($columns) . "</p>";
                        
                        // عدد السجلات
                        $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
                        $count = $stmt->fetchColumn();
                        echo "<p>📋 عدد السجلات: " . number_format($count) . "</p>";
                        
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
                    }
                    
                    echo "</div>";
                }
                
                // عرض تحويل أسماء الأعمدة
                echo "<div class='section'>";
                echo "<h3>🔄 تحويل أسماء الأعمدة</h3>";
                echo "<p>هذا الجدول يوضح كيفية تحويل أسماء الأعمدة العربية إلى أسماء الحقول الإنجليزية:</p>";
                
                $mapping = getColumnMapping();
                echo "<table>";
                echo "<tr><th>الاسم العربي (في CSV)</th><th>اسم الحقل (في قاعدة البيانات)</th></tr>";
                
                foreach ($mapping as $arabic => $english) {
                    echo "<tr>";
                    echo "<td>$arabic</td>";
                    echo "<td>$english</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                echo "<p>📊 إجمالي التحويلات: " . count($mapping) . "</p>";
                echo "</div>";
                
                // اختبار ملف CSV
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_csv'])) {
                    $file = $_FILES['test_csv'];
                    
                    if ($file['error'] === UPLOAD_ERR_OK) {
                        echo "<div class='section'>";
                        echo "<h3>📁 تحليل ملف CSV</h3>";
                        
                        $handle = fopen($file['tmp_name'], 'r');
                        $headers = fgetcsv($handle);
                        
                        // تنظيف أسماء الأعمدة
                        $cleanHeaders = array_map(function($header) {
                            $header = str_replace("\xEF\xBB\xBF", '', $header);
                            return trim($header);
                        }, $headers);
                        
                        echo "<h4>🔍 أسماء الأعمدة في الملف:</h4>";
                        echo "<table>";
                        echo "<tr><th>الرقم</th><th>الاسم الأصلي</th><th>الاسم بعد التنظيف</th><th>التحويل</th><th>موجود في قاعدة البيانات؟</th></tr>";
                        
                        foreach ($cleanHeaders as $index => $header) {
                            $original = $headers[$index];
                            $mapped = isset($mapping[$header]) ? $mapping[$header] : $header;
                            
                            // التحقق من وجود الحقل في قاعدة البيانات
                            $exists = false;
                            try {
                                $stmt = $pdo->query("DESCRIBE accounting");
                                $dbColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                                $exists = in_array($mapped, $dbColumns);
                            } catch (Exception $e) {
                                // تجاهل الخطأ
                            }
                            
                            $status = $exists ? '✅ موجود' : '❌ غير موجود';
                            $rowClass = $exists ? '' : 'style="background: #f8d7da;"';
                            
                            echo "<tr $rowClass>";
                            echo "<td>" . ($index + 1) . "</td>";
                            echo "<td>" . htmlspecialchars($original) . "</td>";
                            echo "<td>" . htmlspecialchars($header) . "</td>";
                            echo "<td>" . htmlspecialchars($mapped) . "</td>";
                            echo "<td>$status</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                        
                        // قراءة عينة من البيانات
                        echo "<h4>📊 عينة من البيانات (أول 3 سجلات):</h4>";
                        echo "<table>";
                        echo "<tr>";
                        foreach (array_slice($cleanHeaders, 0, 10) as $header) {
                            echo "<th>" . htmlspecialchars($header) . "</th>";
                        }
                        echo "</tr>";
                        
                        for ($i = 0; $i < 3; $i++) {
                            $data = fgetcsv($handle);
                            if ($data !== FALSE) {
                                echo "<tr>";
                                foreach (array_slice($data, 0, 10) as $cell) {
                                    echo "<td>" . htmlspecialchars($cell) . "</td>";
                                }
                                echo "</tr>";
                            }
                        }
                        
                        echo "</table>";
                        
                        fclose($handle);
                        echo "</div>";
                    }
                }
                
            } catch (Exception $e) {
                echo "<div class='section error'>";
                echo "<h3>❌ خطأ في الاتصال</h3>";
                echo "<p>" . $e->getMessage() . "</p>";
                echo "</div>";
            }
            ?>
            
            <!-- نموذج اختبار ملف CSV -->
            <div class="section">
                <h3>📤 اختبار ملف CSV</h3>
                <p>ارفع ملف CSV لتحليل أسماء الأعمدة والتحقق من التوافق:</p>
                
                <form method="post" enctype="multipart/form-data">
                    <input type="file" name="test_csv" accept=".csv" required style="margin: 10px 0;">
                    <br>
                    <button type="submit" class="btn">تحليل الملف</button>
                </form>
            </div>
            
            <!-- الحلول المقترحة -->
            <div class="section warning">
                <h3>💡 الحلول المقترحة</h3>
                <h4>إذا كانت المشكلة في أسماء الأعمدة:</h4>
                <ol>
                    <li><strong>تأكد من ترميز الملف:</strong> احفظ ملف CSV بترميز UTF-8 بدون BOM</li>
                    <li><strong>تحقق من أسماء الأعمدة:</strong> يجب أن تطابق الأسماء العربية في جدول التحويل أعلاه</li>
                    <li><strong>إزالة المسافات الزائدة:</strong> تأكد من عدم وجود مسافات في بداية أو نهاية أسماء الأعمدة</li>
                    <li><strong>استخدم محرر نصوص متقدم:</strong> مثل Notepad++ لفحص الملف</li>
                </ol>
                
                <h4>إذا كانت المشكلة في هيكل قاعدة البيانات:</h4>
                <ol>
                    <li>قم بتشغيل <a href="recreate_main_tables.php">إعادة إنشاء الجداول الأساسية</a></li>
                    <li>تأكد من وجود جميع الحقول المطلوبة</li>
                    <li>تحقق من أن الجداول تم إنشاؤها بدون مفتاح أساسي id</li>
                </ol>
            </div>
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="import_data.php" class="btn">استيراد البيانات</a>
                <a href="recreate_main_tables.php" class="btn">إعادة إنشاء الجداول</a>
            </div>
        </div>
    </div>
</body>
</html>
