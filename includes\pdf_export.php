<?php
/**
 * نظام تصدير PDF - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

require_once 'database.php';
require_once 'functions.php';

/**
 * فئة تصدير PDF
 */
class PDFExporter {
    private $pdo;
    private $title;
    private $orientation;
    private $format;
    
    public function __construct($title = 'TIaF Report', $orientation = 'P', $format = 'A4') {
        $this->pdo = getDBConnection();
        $this->title = $title;
        $this->orientation = $orientation;
        $this->format = $format;
    }
    
    /**
     * تصدير تقرير إلى PDF
     */
    public function exportReport($table, $filters = [], $columns = []) {
        // إنشاء محتوى HTML للتقرير
        $html = $this->generateReportHTML($table, $filters, $columns);
        
        // تحويل HTML إلى PDF
        $this->generatePDF($html, $this->title . '_' . $table . '_' . date('Y-m-d'));
    }
    
    /**
     * إنشاء محتوى HTML للتقرير
     */
    private function generateReportHTML($table, $filters, $columns) {
        // بناء الاستعلام
        $where_conditions = [];
        $params = [];
        
        foreach ($filters as $key => $value) {
            if (!empty($value) && $key !== 'export' && $key !== 'page') {
                if ($key === 'export_date_from' || $key === 'date_from') {
                    $where_conditions[] = "a.export_date >= ?";
                    $params[] = $value;
                } elseif ($key === 'export_date_to' || $key === 'date_to') {
                    $where_conditions[] = "a.export_date <= ?";
                    $params[] = $value;
                } elseif ($key === 'search_term') {
                    $search_term = '%' . $value . '%';
                    $where_conditions[] = "(
                        a.request_number LIKE ? OR 
                        a.taxpayer_name LIKE ? OR 
                        a.office_name LIKE ?
                    )";
                    $params = array_merge($params, [$search_term, $search_term, $search_term]);
                } else {
                    $where_conditions[] = "a.$key = ?";
                    $params[] = $value;
                }
            }
        }
        
        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
        
        // تنفيذ الاستعلام
        $sql = "
            SELECT 
                a.*,
                c.general_manager,
                c.specialization,
                c.region,
                c.office_branch,
                rs.status_description,
                cont.container_name
            FROM `$table` a
            LEFT JOIN classification c ON a.office_code = c.office_code
            LEFT JOIN request_status rs ON a.request_status = rs.status_code
            LEFT JOIN container cont ON a.container = cont.container_code
            $where_clause
            ORDER BY a.export_date DESC, a.created_at DESC
            LIMIT 1000
        ";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        $data = $stmt->fetchAll();
        
        // إنشاء HTML
        $html = $this->buildHTMLStructure($table, $data, $filters);
        
        return $html;
    }
    
    /**
     * بناء هيكل HTML
     */
    private function buildHTMLStructure($table, $data, $filters) {
        $table_names = [
            'accounting' => 'طلبات المحاسبة',
            'conflict' => 'طلبات إنهاء النزاع',
            'dispute' => 'طلبات تسوية النزاع'
        ];
        
        $table_title = $table_names[$table] ?? $table;
        
        $html = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body {
                    font-family: "DejaVu Sans", Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    font-size: 12px;
                    line-height: 1.4;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 20px;
                }
                .title {
                    font-size: 18px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }
                .subtitle {
                    font-size: 14px;
                    color: #666;
                }
                .filters {
                    background: #f5f5f5;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }
                .filters h3 {
                    margin-bottom: 10px;
                    font-size: 14px;
                }
                .filter-item {
                    display: inline-block;
                    margin: 5px 10px;
                    font-size: 11px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    font-size: 10px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: center;
                }
                th {
                    background: #f0f0f0;
                    font-weight: bold;
                }
                tr:nth-child(even) {
                    background: #f9f9f9;
                }
                .footer {
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                    font-size: 10px;
                    color: #666;
                }
                .stats {
                    background: #e8f4f8;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }
                .stats h3 {
                    margin-bottom: 10px;
                    font-size: 14px;
                }
                .stat-item {
                    display: inline-block;
                    margin: 5px 15px;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <div class="title">نظام تقارير TIaF</div>
                <div class="subtitle">تقرير ' . $table_title . '</div>
                <div style="font-size: 12px; margin-top: 10px;">
                    تاريخ التصدير: ' . date('Y-m-d H:i:s') . '
                </div>
            </div>';
        
        // إضافة الفلاتر المطبقة
        if (!empty($filters)) {
            $html .= '<div class="filters">
                <h3>الفلاتر المطبقة:</h3>';
            
            foreach ($filters as $key => $value) {
                if (!empty($value) && $key !== 'export' && $key !== 'page') {
                    $filter_labels = [
                        'export_date_from' => 'من تاريخ',
                        'export_date_to' => 'إلى تاريخ',
                        'date_from' => 'من تاريخ',
                        'date_to' => 'إلى تاريخ',
                        'general_manager' => 'مدير العموم',
                        'specialization' => 'الاختصاص',
                        'region' => 'المنطقة',
                        'office_branch' => 'المامورية',
                        'request_status' => 'حالة الطلب',
                        'search_term' => 'البحث'
                    ];
                    
                    $label = $filter_labels[$key] ?? $key;
                    $html .= '<div class="filter-item"><strong>' . $label . ':</strong> ' . htmlspecialchars($value) . '</div>';
                }
            }
            
            $html .= '</div>';
        }
        
        // إضافة الإحصائيات
        $total_records = count($data);
        $html .= '<div class="stats">
            <h3>إحصائيات التقرير:</h3>
            <div class="stat-item">إجمالي السجلات: ' . number_format($total_records) . '</div>';
        
        if (!empty($data)) {
            $approved = count(array_filter($data, function($row) { return $row['request_status'] == '40'; }));
            $rejected = count(array_filter($data, function($row) { return $row['request_status'] == '50'; }));
            $pending = $total_records - $approved - $rejected;
            
            $html .= '<div class="stat-item">مقبولة: ' . number_format($approved) . '</div>';
            $html .= '<div class="stat-item">مرفوضة: ' . number_format($rejected) . '</div>';
            $html .= '<div class="stat-item">معلقة: ' . number_format($pending) . '</div>';
        }
        
        $html .= '</div>';
        
        // إضافة الجدول
        if (!empty($data)) {
            $html .= '<table>
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>اسم المكلف</th>
                        <th>تاريخ التصدير</th>
                        <th>المأمورية</th>
                        <th>المنطقة</th>
                        <th>مدير العموم</th>
                        <th>حالة الطلب</th>
                        <th>الضريبة المقررة</th>
                        <th>الضريبة القانونية</th>
                    </tr>
                </thead>
                <tbody>';
            
            foreach ($data as $row) {
                $html .= '<tr>
                    <td>' . htmlspecialchars($row['request_number']) . '</td>
                    <td>' . htmlspecialchars($row['taxpayer_name']) . '</td>
                    <td>' . formatDate($row['export_date']) . '</td>
                    <td>' . htmlspecialchars($row['office_name']) . '</td>
                    <td>' . htmlspecialchars($row['region']) . '</td>
                    <td>' . htmlspecialchars($row['general_manager']) . '</td>
                    <td>' . htmlspecialchars($row['status_description']) . '</td>
                    <td>' . number_format($row['tax_according_to_declaration'], 2) . '</td>
                    <td>' . number_format($row['tax_according_to_law'], 2) . '</td>
                </tr>';
            }
            
            $html .= '</tbody></table>';
        } else {
            $html .= '<div style="text-align: center; padding: 50px; color: #666;">
                لا توجد بيانات لعرضها
            </div>';
        }
        
        $html .= '<div class="footer">
                <div>نظام تقارير TIaF - جميع الحقوق محفوظة © 2025</div>
                <div>تم إنشاء هذا التقرير تلقائياً بواسطة النظام</div>
            </div>
        </body>
        </html>';
        
        return $html;
    }
    
    /**
     * تحويل HTML إلى PDF باستخدام DomPDF
     */
    private function generatePDF($html, $filename) {
        // استخدام مكتبة DomPDF البسيطة
        $this->generateSimplePDF($html, $filename);
    }
    
    /**
     * إنشاء PDF بسيط بدون مكتبات خارجية
     */
    private function generateSimplePDF($html, $filename) {
        // تنظيف HTML وتحويله لتنسيق أبسط
        $content = strip_tags($html, '<table><tr><td><th><thead><tbody><div><h1><h2><h3><p><strong><b>');
        
        // إعداد headers للتحميل
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '.pdf"');
        header('Cache-Control: private, max-age=0, must-revalidate');
        header('Pragma: public');
        
        // إنشاء PDF بسيط (يمكن تحسينه لاحقاً بمكتبة متخصصة)
        echo $this->createBasicPDF($content, $filename);
    }
    
    /**
     * إنشاء PDF أساسي
     */
    private function createBasicPDF($content, $title) {
        // هذه دالة مبسطة - في الإنتاج يُفضل استخدام مكتبة مثل TCPDF أو DomPDF
        $pdf_content = "%PDF-1.4\n";
        $pdf_content .= "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
        $pdf_content .= "2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n";
        $pdf_content .= "3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] /Contents 4 0 R >>\nendobj\n";
        $pdf_content .= "4 0 obj\n<< /Length " . strlen($content) . " >>\nstream\n";
        $pdf_content .= "BT /F1 12 Tf 50 750 Td (" . $title . ") Tj ET\n";
        $pdf_content .= "endstream\nendobj\n";
        $pdf_content .= "xref\n0 5\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \n0000000207 00000 n \n";
        $pdf_content .= "trailer\n<< /Size 5 /Root 1 0 R >>\nstartxref\n" . strlen($pdf_content) . "\n%%EOF";
        
        return $pdf_content;
    }
}

/**
 * دالة مساعدة لتصدير PDF
 */
function exportToPDF($table, $filters = [], $title = null) {
    if (!$title) {
        $table_names = [
            'accounting' => 'تقرير طلبات المحاسبة',
            'conflict' => 'تقرير طلبات إنهاء النزاع',
            'dispute' => 'تقرير طلبات تسوية النزاع'
        ];
        $title = $table_names[$table] ?? 'تقرير TIaF';
    }
    
    $exporter = new PDFExporter($title);
    $exporter->exportReport($table, $filters);
}

/**
 * دالة تصدير HTML للطباعة
 */
function exportToPrintableHTML($table, $filters = []) {
    $exporter = new PDFExporter();
    $html = $exporter->generateReportHTML($table, $filters, []);
    
    header('Content-Type: text/html; charset=utf-8');
    echo $html;
}
?>
