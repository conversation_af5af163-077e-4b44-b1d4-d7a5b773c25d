# TIaF Report System
## نظام تقارير الضرائب والرسوم

### 📋 وصف المشروع
نظام ويب متكامل مطور بلغة PHP لعرض تقارير وبيانات إحصائية لطلبات المحاسبة وتسوية وإنهاء النزاعات الضريبية. النظام موجه للمستخدم العربي مع تصميم عصري ومريح.

### 🚀 المميزات الرئيسية
- **واجهة عربية كاملة** مع دعم اتجاه RTL
- **لوحة تحكم تفاعلية** مع مؤشرات الأداء (KPIs)
- **مخططات بيانية تفاعلية** باستخدام Chart.js
- **استيراد البيانات** من ملفات CSV
- **تقارير منفصلة** لكل نوع من أنواع الطلبات
- **بحث متقدم** وتصفية البيانات
- **تصميم متجاوب** مع جميع أحجام الشاشات
- **وضع ليلي/نهاري** قابل للتبديل

### 🛠️ التقنيات المستخدمة
- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Charts**: Chart.js
- **Fonts**: Tajawal, Cairo (للعربية) + Fira Sans, Poppins (للاتينية)
- **Server**: Apache/Nginx + XAMPP

### 📁 هيكل المشروع
```
TIaF-Report/
├── assets/
│   ├── css/
│   │   └── main.css
│   ├── js/
│   └── fonts/
├── config/
│   └── database.php
├── includes/
│   └── functions.php
├── reports/
│   ├── accounting.php
│   ├── conflict.php
│   └── dispute.php
├── dashboard/
├── admin/
├── Main Table/
│   ├── AccountingEXPORT_YYYYMMDD.csv
│   ├── ConflictEXPORT_YYYYMMDD.csv
│   └── DisputeEXPORT_YYYYMMDD.csv
├── Mapping Table/
│   ├── جدول التصنيف.csv
│   ├── جدول الوعاء.csv
│   └── جدول حالة الطلب.csv
├── database_setup.php
├── database_setup_part2.php
├── database_setup_part3.php
├── import_data.php
├── index.php
└── README.md
```

### 🗄️ قاعدة البيانات
#### الجداول المرجعية:
- `classification` - جدول التصنيف
- `container` - جدول الوعاء
- `request_status` - جدول حالة الطلب
- `system_settings` - إعدادات النظام

#### الجداول الأساسية:
- `accounting` - طلبات المحاسبة (بدون قيود منفردة)
- `conflict` - طلبات إنهاء النزاع (بدون قيود منفردة)
- `dispute` - طلبات تسوية النزاع (بدون قيود منفردة)

**ملاحظة مهمة:** الجداول الأساسية تم إنشاؤها بدون قيود أو مفاتيح منفردة لتسهيل الاستيراد المستمر للبيانات دون تعارضات.

### ⚙️ التثبيت والإعداد

#### المتطلبات:
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx
- XAMPP (للتطوير المحلي)

#### خطوات التثبيت:

1. **نسخ المشروع**
   ```bash
   git clone [repository-url]
   cd TIaF-Report
   ```

2. **إعداد قاعدة البيانات**
   - تشغيل XAMPP
   - فتح المتصفح والذهاب إلى: `http://localhost/TIaF-Report`
   - النقر على "إعداد قاعدة البيانات"
   - اتباع الخطوات الثلاث لإنشاء قاعدة البيانات

3. **تكوين الإعدادات**
   - تحرير ملف `config/database.php`
   - تحديث بيانات الاتصال بقاعدة البيانات حسب الحاجة

4. **استيراد البيانات**
   - الذهاب إلى صفحة "استيراد البيانات"
   - رفع ملفات CSV للجداول الثلاثة
   - التأكد من تنسيق أسماء الملفات: `[TableName]EXPORT_YYYYMMDD.csv`
   - اختيار "حذف جميع البيانات السابقة" إذا كنت تريد استبدال البيانات بالكامل
   - أو ترك الخيار فارغاً لحذف بيانات نفس التاريخ فقط

### 📊 استخدام النظام

#### 1. لوحة التحكم
- عرض الإحصائيات العامة
- مخططات بيانية تفاعلية
- مؤشرات الأداء الرئيسية

#### 2. التقارير
- **تقارير المحاسبة**: عرض وتحليل طلبات المحاسبة
- **تقارير إنهاء النزاع**: عرض وتحليل طلبات إنهاء النزاع
- **تقارير تسوية النزاع**: عرض وتحليل طلبات تسوية النزاع

#### 3. البحث والتصفية
- البحث برقم الطلب
- التصفية حسب المأمورية
- التصفية حسب حالة الطلب
- التصفية حسب تاريخ التصدير

#### 4. التصدير
- تصدير النتائج إلى CSV
- تصدير التقارير إلى PDF (قريباً)

#### 5. إدارة البيانات
- عرض إحصائيات الجداول وأحجامها
- حذف جدول كامل
- حذف بيانات تاريخ محدد
- تحسين أداء الجداول

### 🎨 التخصيص

#### الألوان:
- الأساسي: `#667eea` إلى `#764ba2`
- النجاح: `#28a745`
- التحذير: `#ffc107`
- الخطر: `#dc3545`
- المعلومات: `#17a2b8`

#### الخطوط:
- العربية: Tajawal, Cairo
- اللاتينية: Fira Sans, Poppins

### 🔧 الصيانة والتحديث

#### تحديث البيانات:
1. تصدير البيانات الجديدة من النظام الأساسي
2. رفع الملفات عبر صفحة "استيراد البيانات"
3. التأكد من تنسيق التاريخ في اسم الملف

#### النسخ الاحتياطي:
```sql
mysqldump -u root -p tiaf_db > backup_YYYYMMDD.sql
```

#### الاستعادة:
```sql
mysql -u root -p tiaf_db < backup_YYYYMMDD.sql
```

### 🐛 استكشاف الأخطاء

#### مشاكل شائعة:
1. **خطأ في الاتصال بقاعدة البيانات**
   - التأكد من تشغيل MySQL
   - مراجعة إعدادات الاتصال في `config/database.php`

2. **مشكلة في استيراد البيانات**
   - التأكد من تنسيق ملف CSV
   - التأكد من تنسيق اسم الملف
   - مراجعة أذونات المجلدات

3. **مشكلة في عرض الخطوط العربية**
   - التأكد من ترميز UTF-8
   - التأكد من اتصال الإنترنت لتحميل Google Fonts

### 📞 الدعم والمساعدة
- **المطور**: TIaF Development Team
- **الإصدار**: 1.0
- **التاريخ**: 2025-06-17

### 📄 الترخيص
جميع الحقوق محفوظة © 2025 TIaF Report System

### 🔄 التحديثات المستقبلية
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تصدير التقارير إلى PDF
- [ ] إضافة المزيد من المخططات البيانية
- [ ] تطوير API للتكامل مع الأنظمة الأخرى
- [ ] إضافة الإشعارات والتنبيهات
- [ ] تحسين الأداء والتخزين المؤقت
