<?php
/**
 * تقارير طلبات إنهاء النزاع - TIaF Report System
 *
 * <AUTHOR> Development Team
 * @version 1.3
 * @date 2025-06-17
 */

require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/pdf_export.php';

// معالجة التصدير
if (isset($_GET['export'])) {
    $export_filters = $_GET;
    unset($export_filters['export']);

    if ($_GET['export'] === 'csv') {
        exportToCSV('conflict', $export_filters);
        exit;
    } elseif ($_GET['export'] === 'pdf') {
        exportToPDF('conflict', $export_filters, 'تقرير طلبات إنهاء النزاع');
        exit;
    } elseif ($_GET['export'] === 'print') {
        exportToPrintableHTML('conflict', $export_filters);
        exit;
    }
}

// معالجة الفلاتر
$filters = [
    'export_date_from' => $_GET['export_date_from'] ?? '',
    'export_date_to' => $_GET['export_date_to'] ?? '',
    'general_manager' => $_GET['general_manager'] ?? '',
    'specialization' => $_GET['specialization'] ?? '',
    'region' => $_GET['region'] ?? '',
    'office_branch' => $_GET['office_branch'] ?? '',
    'office_code' => $_GET['office_code'] ?? '',
    'request_status' => $_GET['request_status'] ?? '',
    'container' => $_GET['container'] ?? '',
    'page' => $_GET['page'] ?? 1
];

// الحصول على البيانات
try {
    $pdo = getDBConnection();
    
    // بناء الاستعلام مع الفلاتر
    $where_conditions = [];
    $params = [];
    
    if (!empty($filters['export_date_from'])) {
        $where_conditions[] = "a.export_date >= ?";
        $params[] = $filters['export_date_from'];
    }
    
    if (!empty($filters['export_date_to'])) {
        $where_conditions[] = "a.export_date <= ?";
        $params[] = $filters['export_date_to'];
    }
    
    if (!empty($filters['general_manager'])) {
        $where_conditions[] = "c.general_manager = ?";
        $params[] = $filters['general_manager'];
    }
    
    if (!empty($filters['specialization'])) {
        $where_conditions[] = "c.specialization = ?";
        $params[] = $filters['specialization'];
    }
    
    if (!empty($filters['region'])) {
        $where_conditions[] = "c.region = ?";
        $params[] = $filters['region'];
    }
    
    if (!empty($filters['office_branch'])) {
        $where_conditions[] = "c.office_branch = ?";
        $params[] = $filters['office_branch'];
    }
    
    if (!empty($filters['office_code'])) {
        $where_conditions[] = "a.office_code = ?";
        $params[] = $filters['office_code'];
    }
    
    if (!empty($filters['request_status'])) {
        $where_conditions[] = "a.request_status = ?";
        $params[] = $filters['request_status'];
    }
    
    if (!empty($filters['container'])) {
        $where_conditions[] = "a.container = ?";
        $params[] = $filters['container'];
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // حساب إجمالي السجلات
    $count_sql = "
        SELECT COUNT(*)
        FROM conflict a
        LEFT JOIN classification c ON a.office_code = c.office_code
        $where_clause
    ";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($params);
    $total_records = $stmt->fetchColumn();
    
    // حساب الصفحات
    $records_per_page = 50;
    $total_pages = ceil($total_records / $records_per_page);
    $current_page = max(1, min($total_pages, (int)$filters['page']));
    $offset = ($current_page - 1) * $records_per_page;
    
    // جلب البيانات
    $data_sql = "
        SELECT 
            a.*,
            c.general_manager,
            c.specialization,
            c.region,
            c.office_branch,
            rs.status_description,
            cont.container_name
        FROM conflict a
        LEFT JOIN classification c ON a.office_code = c.office_code
        LEFT JOIN request_status rs ON a.request_status = rs.status_code
        LEFT JOIN container cont ON a.container = cont.container_code
        $where_clause
        ORDER BY a.export_date DESC, a.created_at DESC
        LIMIT $records_per_page OFFSET $offset
    ";
    $stmt = $pdo->prepare($data_sql);
    $stmt->execute($params);
    $records = $stmt->fetchAll();
    
    // الحصول على قوائم الفلاتر
    $managers_list = $pdo->query("SELECT DISTINCT general_manager FROM classification ORDER BY general_manager")->fetchAll();
    $specializations_list = $pdo->query("SELECT DISTINCT specialization FROM classification ORDER BY specialization")->fetchAll();
    $regions_list = $pdo->query("SELECT DISTINCT region FROM classification ORDER BY region")->fetchAll();
    $branches_list = $pdo->query("SELECT DISTINCT office_branch FROM classification ORDER BY office_branch")->fetchAll();
    $offices_list = $pdo->query("SELECT DISTINCT office_code, office_name FROM classification ORDER BY office_name")->fetchAll();
    $status_list = $pdo->query("SELECT * FROM request_status ORDER BY status_code")->fetchAll();
    $containers_list = $pdo->query("SELECT * FROM container ORDER BY container_code")->fetchAll();
    
    // إحصائيات سريعة
    $stats_sql = "
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN a.request_status = '40' THEN 1 ELSE 0 END) as approved,
            SUM(CASE WHEN a.request_status = '50' THEN 1 ELSE 0 END) as rejected,
            SUM(CASE WHEN a.request_status IN ('20', '21', '22', '30') THEN 1 ELSE 0 END) as pending,
            SUM(a.tax_according_to_declaration) as total_declared_tax,
            SUM(a.tax_according_to_law) as total_law_tax
        FROM conflict a
        LEFT JOIN classification c ON a.office_code = c.office_code
        $where_clause
    ";
    $stmt = $pdo->prepare($stats_sql);
    $stmt->execute($params);
    $stats = $stmt->fetch();
    
} catch (Exception $e) {
    $error_message = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقارير طلبات إنهاء النزاع - TIaF Report System</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../assets/css/main.css">
    <style>
        .filters-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .table-responsive {
            overflow-x: auto;
            margin: 20px 0;
        }
        
        .table {
            font-size: 0.85rem;
        }
        
        .table th {
            background: #667eea;
            color: white;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
        }
        
        .table td {
            text-align: center;
            vertical-align: middle;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }
        
        .pagination a, .pagination span {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            text-decoration: none;
            color: #667eea;
        }
        
        .pagination .current {
            background: #667eea;
            color: white;
        }
        
        .export-buttons {
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1>⚖️ تقارير طلبات إنهاء النزاع</h1>
                <p>عرض وتحليل بيانات طلبات إنهاء النزاع مع إمكانيات التصفية المتقدمة</p>
            </div>
            
            <div class="card-body">
                <?php if (isset($error_message)): ?>
                <div class="alert alert-danger">
                    <h4>❌ خطأ في تحميل البيانات</h4>
                    <p><?php echo $error_message; ?></p>
                </div>
                <?php else: ?>
                
                <!-- الإحصائيات السريعة -->
                <h3>📈 الإحصائيات السريعة</h3>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['total']); ?></div>
                        <div class="stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['approved']); ?></div>
                        <div class="stat-label">طلبات مقبولة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['rejected']); ?></div>
                        <div class="stat-label">طلبات مرفوضة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['pending']); ?></div>
                        <div class="stat-label">طلبات معلقة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['total_declared_tax'], 0); ?></div>
                        <div class="stat-label">إجمالي الضريبة المقررة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($stats['total_law_tax'], 0); ?></div>
                        <div class="stat-label">إجمالي الضريبة القانونية</div>
                    </div>
                </div>
                
                <!-- فلاتر البحث -->
                <div class="filters-section">
                    <h3>🔍 فلاتر البحث والتصفية</h3>
                    <form method="GET" action="">
                        <div class="filter-grid">
                            <div class="form-group">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" name="export_date_from" class="form-control" value="<?php echo htmlspecialchars($filters['export_date_from']); ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" name="export_date_to" class="form-control" value="<?php echo htmlspecialchars($filters['export_date_to']); ?>">
                            </div>
                            <div class="form-group">
                                <label class="form-label">مديري العموم</label>
                                <select name="general_manager" class="form-control">
                                    <option value="">جميع مديري العموم</option>
                                    <?php foreach ($managers_list as $manager): ?>
                                    <option value="<?php echo htmlspecialchars($manager['general_manager']); ?>" <?php echo $filters['general_manager'] === $manager['general_manager'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($manager['general_manager']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">الاختصاص</label>
                                <select name="specialization" class="form-control">
                                    <option value="">جميع الاختصاصات</option>
                                    <?php foreach ($specializations_list as $spec): ?>
                                    <option value="<?php echo htmlspecialchars($spec['specialization']); ?>" <?php echo $filters['specialization'] === $spec['specialization'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($spec['specialization']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">المنطقة</label>
                                <select name="region" class="form-control">
                                    <option value="">جميع المناطق</option>
                                    <?php foreach ($regions_list as $region): ?>
                                    <option value="<?php echo htmlspecialchars($region['region']); ?>" <?php echo $filters['region'] === $region['region'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($region['region']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">المامورية</label>
                                <select name="office_branch" class="form-control">
                                    <option value="">جميع المأموريات</option>
                                    <?php foreach ($branches_list as $branch): ?>
                                    <option value="<?php echo htmlspecialchars($branch['office_branch']); ?>" <?php echo $filters['office_branch'] === $branch['office_branch'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($branch['office_branch']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div style="text-align: center; margin-top: 20px;">
                            <button type="submit" class="btn btn-primary">🔍 تطبيق الفلاتر</button>
                            <a href="accounting.php" class="btn btn-light">🔄 إعادة تعيين</a>
                        </div>
                    </form>
                </div>
                
                <!-- أزرار التصدير -->
                <div class="export-buttons">
                    <a href="?<?php echo http_build_query(array_merge($filters, ['export' => 'csv'])); ?>" class="btn btn-success">📄 تصدير CSV</a>
                    <a href="?<?php echo http_build_query(array_merge($filters, ['export' => 'pdf'])); ?>" class="btn btn-danger">📑 تصدير PDF</a>
                </div>
                
                <!-- جدول البيانات -->
                <h3>📋 البيانات (<?php echo number_format($total_records); ?> سجل)</h3>
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>تاريخ التصدير</th>
                                <th>اسم المكلف</th>
                                <th>المأمورية</th>
                                <th>المنطقة</th>
                                <th>مدير العموم</th>
                                <th>حالة الطلب</th>
                                <th>الوعاء</th>
                                <th>الضريبة المقررة</th>
                                <th>الضريبة القانونية</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($records as $record): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($record['request_number']); ?></td>
                                <td><?php echo formatDate($record['export_date']); ?></td>
                                <td><?php echo htmlspecialchars($record['taxpayer_name']); ?></td>
                                <td><?php echo htmlspecialchars($record['office_name']); ?></td>
                                <td><?php echo htmlspecialchars($record['region']); ?></td>
                                <td><?php echo htmlspecialchars($record['general_manager']); ?></td>
                                <td><?php echo htmlspecialchars($record['status_description']); ?></td>
                                <td><?php echo htmlspecialchars($record['container_name']); ?></td>
                                <td><?php echo number_format($record['tax_according_to_declaration'], 2); ?></td>
                                <td><?php echo number_format($record['tax_according_to_law'], 2); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- الترقيم -->
                <?php if ($total_pages > 1): ?>
                <div class="pagination">
                    <?php if ($current_page > 1): ?>
                    <a href="?<?php echo http_build_query(array_merge($filters, ['page' => $current_page - 1])); ?>">السابق</a>
                    <?php endif; ?>
                    
                    <?php for ($i = max(1, $current_page - 2); $i <= min($total_pages, $current_page + 2); $i++): ?>
                    <?php if ($i == $current_page): ?>
                    <span class="current"><?php echo $i; ?></span>
                    <?php else: ?>
                    <a href="?<?php echo http_build_query(array_merge($filters, ['page' => $i])); ?>"><?php echo $i; ?></a>
                    <?php endif; ?>
                    <?php endfor; ?>
                    
                    <?php if ($current_page < $total_pages): ?>
                    <a href="?<?php echo http_build_query(array_merge($filters, ['page' => $current_page + 1])); ?>">التالي</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
                <?php endif; ?>
                
                <div style="text-align: center; margin-top: 30px;">
                    <a href="../index.php" class="btn">العودة للصفحة الرئيسية</a>
                    <a href="../dashboard.php" class="btn btn-info">لوحة التحكم</a>
                    <a href="accounting.php" class="btn btn-info">تقارير المحاسبة</a>
                    <a href="dispute.php" class="btn btn-success">تقارير تسوية النزاع</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
