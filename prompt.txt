التحليل
****
تحليل جميع ملفات csv داخل مجلد Mapping Table في ملف mapping_table_analysis.php
انشاء جدول اضافي يوضح الربط بين الجداول حقول الربط لفهم مخطط البرنامج
انشاء مخطط (ERD) رسومي (بصيغة PlantUML) باسم erd_mapping_table.puml يوضح العلاقات بين الجداول الرئيسية والمرجعية.



تحليل جميع ملفات csv داخل مجلد Main Table في ملف main_table_analysis.php
انشاء جدول اضافي يوضح الربط بين الجداول حقول الربط لفهم مخطط البرنامج
انشاء مخطط (ERD) رسومي (بصيغة PlantUML) باسم erd_mapping_table.puml يوضح العلاقات بين الجداول الرئيسية والمرجعية.


اقتراح تصميم (بناء) قاعدة بيانات بناءً على تحليل الجداول والعلاقات المرجعية.
هذا التحليل بالتفصيل في شكل جدولى وخطوات واضحة في ملف system_analysis.php
بناء على جميع الجداول CSV 
الموجودة داخل مجلد Main Table
مع الربط في الجداول المساعدة الموجودة داخل مجلد Mapping Table
لفهم طريقة عمل الموقع

خطوات تنفيذ قاعدة البيانات
*****************
اعداد ملف لانشاء قاعدة البيانات والجداول المطلوبة
database_setup.php
طبقا للآتي :

ملاحظات يجب اخذها في الحسبان عند تطبيق المشروع :

أنشاء موقع ويب متكامل بلغة PHP + HTML + CSS + JavaScript+sql (phpmyadmain)
لعرض تقارير وبيانات إحصائية، موجه للمستخدم العربي، مع تصميم عصري ومريح (Vibe Coding).
التنظيم والنظافة (clean, readable code)
الجو الإبداعي والعفوي (vibe-style)
سهل الترتيب 
مجلدات منفصلة للملفات
سهولة للمستخدم ومتجاوب مع كل الشاشات
🖋️ الخطوط (Fonts) – يجب استخدامها بعناية:

● الخط الأساسي العربي:
استخدم أحد الخطوط التالية لعرض النصوص العربية بشكل أنيق وسهل القراءة:

    Tajawal (أنسب خيار عام للداشبوردات)

    أو Cairo (إذا كانت الواجهة أكثر رسمية)

● الخط اللاتيني الثانوي (للأرقام أو التفاصيل):

    Fira Sans أو Poppins


✅ المتطلبات الأساسية:
1.	اللغة: واجهة الموقع باللغة العربية (اتجاه RTL)، مع دعم كامل للعناوين والنصوص بالعربية.
2.	صفحة رئيسية تحتوي على لوحة تحكم Dashboard تعرض ما يلي:
o	بطاقات (Cards) لعرض مؤشرات الأداء (KPIs) 
o	مخططات بيانية تفاعلية (أعمدة، خطية، دائرية) باستخدام مكتبة مثل Chart.js
o	جدول بيانات إحصائي قابل للتصفية والفرز
o	رسم بياني زمني (Time Series) إن أمكن
3.	زر التبديل بين الوضع الليلي والفاتح (Light/Dark Mode Toggle)
4.	واجهة مستخدم مريحة (UI)، تعتمد على:
o	Flexbox أو CSS Grid
o	انتقالات بسيطة (transitions/animations) للعناصر عند الظهور
o	نظام ألوان هادئ (رمادي ناعم + بنفسجي + أخضر فاتح)

📁 هيكل الملفات:
•	index.php: الصفحة الرئيسية
•	style.css: تنسيق التصميم والأنماط
•	script.js: التبديل بين الأوضاع والتفاعل مع البيانات
•	(مجلد assets للصور أو الرسوم البيانية)

💡 ملاحظات تقنية:
•	لا تستخدم مكتبات ضخمة مثل Bootstrap
•	اجعل الكود نظيفًا ومنظمًا (Vibe Coding)


الهدف: موقع ويب مخصص للمستخدم العربي، يعرض بيانات وتقارير بشكل أنيق، واضح، وتحليلي، 
مع واجهة قابلة للتفاعل تتيح للمستخدم فهم النتائج بسرعة وراحة.


التزم بحقول الجداول وعدم اضافة اى حقول قبل الرجوع اليا

المرحلة الاولي:
إنشاء قاعدة البيانات (مثلاً: tiaf_db).
إنشاء الجداول المرجعية أولاً (classification, container, request_status).
إنشاء جدول الطلبات (requests) مع جميع الحقول المطلوبة.
تحديد المفاتيح الأساسية (PRIMARY KEY) لكل جدول.
تحديد المفاتيح الأجنبية (FOREIGN KEY) للعلاقات المرجعية بين الجداول.
تعبئة الجداول المرجعية بالبيانات الثابتة أولاً.
إدخال بيانات المعاملات في جدول الطلبات (requests) مع مراعاة التكامل المرجعي.
إنشاء الفهارس (Indexes) على الحقول المستخدمة في البحث بكثرة (مثل: office_code, status_id, container_id).
تحديث التصميم حسب الحاجة عند إضافة متطلبات جديدة أو جداول إضافية.


المرحلة الثانية:
المطلوب انشاء الجداول التالية في قاعدة البيانات طبقا لملفات ال CSV داخل مجلد Main Table
الجدول الاول : طلبات المحاسبة Accounting
الجدول الثاني : طلبات تسوية النزاع Dispute
الجدول الثالث : طلبات انهاء النزاع Conflict

بدون اى قيود او مفاتيح منفرده لاننا سنقوم باستيراد هذه الملفات بصفه مستمره.
 مع العلم باننا سنقوم باضافة حقل باسم Export Date لكل من الجداول الثلاثة السابقة
على ان يكون بيانات هذا الحقل ماخوذة من اسم الملف على سبيل المثال
جدول AccountingEXPORT_********
يكون محتوى حقل Export Date = ******** ولكن في شكل تاريخ 
dd-mm-yyyy وهكذا بالنسبة لباقي الجداول الثلاثة
ويتم تغيير بيانات التاريخ طبقا لاسم الملف الذى تم استيراده وهكذا 

وصفحه لاستيراد ملفات الجداول الثلاثة السابقة طبقا للمسمى الموجود للجداول الثلاثة 

المرحلة الثالثة :
مع العلم باننا سنقوم بانشاء تقارير منفصلة لكل جدول من الجداول الثلاثة 
طلبات المحاسبة
طلبات انهاء النزاع
طلبات تسوية النزاع

مع عمل تقارير احصائية لكل نوع من الجداول منفصلة

وسنقوم بربط كل التقاير والاحصائيات والداش بورد بالجداول الثلاثة الموجوده في مجلد Mapping Taple
على مستوى التاريخ
مديري العموم
الاختصاص
المنطقة
المامورية
