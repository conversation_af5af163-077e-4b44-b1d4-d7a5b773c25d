# سجل التغييرات - TIaF Report System

## الإصدار 1.4.2 - 2025-06-17

### 🔧 إصلاح مشاكل استيراد CSV

#### 📁 حل مشكلة أسماء الأعمدة
- **إصلاح مشكلة BOM** (Byte Order Mark) في ملفات CSV
- **تحويل أسماء الأعمدة العربية** إلى أسماء الحقول الإنجليزية
- **تنظيف أسماء الأعمدة** وإزالة المسافات والرموز الخفية
- **معالجة الأخطاء** مثل "Unknown column '﻿رقم الطلب'"

#### 🛠️ أدوات جديدة للتشخيص والإصلاح:

##### 🔍 أداة تشخيص الاستيراد (`debug_import.php`):
- **عرض هيكل الجداول** في قاعدة البيانات
- **تحليل ملفات CSV** وأسماء الأعمدة
- **جدول تحويل الأسماء** العربية إلى الإنجليزية
- **التحقق من التوافق** بين CSV وقاعدة البيانات
- **عرض عينة من البيانات** للفحص

##### 🔧 أداة إصلاح CSV (`fix_csv.php`):
- **إزالة BOM** من ملفات CSV
- **تنظيف أسماء الأعمدة** تلقائياً
- **إزالة الأسطر الفارغة**
- **تحميل الملف المُصحح** مباشرة

#### 📋 تحويل أسماء الأعمدة:
```php
'رقم الطلب' => 'request_number'
'اسم المكلف' => 'taxpayer_name'
'كود المأمورية' => 'office_code'
'اسم المأمورية' => 'office_name'
'حالة الطلب' => 'request_status'
// ... والمزيد
```

#### 🔄 تحسينات الاستيراد:
- **معالجة BOM** تلقائياً أثناء الاستيراد
- **تحويل أسماء الأعمدة** تلقائياً
- **رسائل خطأ أوضح** مع تفاصيل المشكلة
- **عرض تقدم العملية** مع إحصائيات مفصلة

#### 📁 الملفات الجديدة:
- `debug_import.php` - أداة تشخيص مشاكل الاستيراد
- `fix_csv.php` - أداة إصلاح ملفات CSV
- `download_fixed.php` - تحميل الملفات المُصححة

#### 📁 الملفات المحدثة:
- `import_data.php` - إضافة معالجة BOM وتحويل أسماء الأعمدة
- `index.php` - إضافة روابط الأدوات الجديدة
- `CHANGELOG.md` - توثيق الإصلاحات

#### ✅ الفوائد:
- **حل نهائي** لمشاكل استيراد CSV
- **تشخيص سريع** للمشاكل
- **إصلاح تلقائي** للملفات
- **سهولة الاستخدام** مع واجهات بديهية

#### 🎯 كيفية الاستخدام:
1. **إذا فشل الاستيراد**: استخدم `debug_import.php` لتشخيص المشكلة
2. **إذا كانت مشكلة BOM**: استخدم `fix_csv.php` لإصلاح الملف
3. **ثم قم بالاستيراد** باستخدام الملف المُصحح

---

## الإصدار 1.4.1 - 2025-06-17

### 🔧 إصلاحات مهمة

#### 📑 إصلاح نظام تصدير PDF
- **إصلاح مسارات الملفات** في `includes/pdf_export.php`
- **تحسين نظام الطباعة** بدلاً من PDF معقد
- **إضافة CSS للطباعة** مع تنسيق محسن
- **دعم الطباعة التلقائية** عند فتح التقرير

#### 🧪 إضافة نظام اختبار شامل
- **ملف اختبار جديد**: `test_system.php`
- **فحص الاتصال بقاعدة البيانات**
- **التحقق من وجود الجداول**
- **اختبار الدوال المهمة**
- **فحص الملفات الأساسية**
- **تقرير مفصل** عن حالة النظام

#### 🛠️ التحسينات التقنية:
- تصحيح مسارات `require_once` في ملفات PDF
- تحسين دالة `exportToPrintableHTML()`
- إضافة CSS محسن للطباعة
- تحسين معالجة الأخطاء

#### 📁 الملفات المحدثة:
- `includes/pdf_export.php` - إصلاح المسارات وتحسين الطباعة
- `index.php` - إضافة رابط اختبار النظام
- `test_system.php` - ملف اختبار شامل جديد
- `CHANGELOG.md` - توثيق الإصلاحات

#### ✅ الفوائد:
- **حل مشكلة تصدير PDF** نهائياً
- **نظام اختبار شامل** لتشخيص المشاكل
- **طباعة محسنة** للتقارير
- **سهولة الصيانة** والتشخيص

---

## الإصدار 1.4 - 2025-06-17

### 🔧 تصحيح هيكل الجداول الأساسية

#### ⚠️ تغيير مهم في هيكل قاعدة البيانات
- **إزالة المفتاح الأساسي id** من الجداول الثلاثة الأساسية
- **تنظيم البيانات حسب export_date** المستخرج من اسم الملف
- **تنسيق أسماء الملفات الصحيح**: `TableNameEXPORT_YYYYMMDD.csv`

#### 📁 أمثلة أسماء الملفات:
- `AccountingEXPORT_20250526.csv`
- `ConflictEXPORT_20250526.csv`
- `DisputeEXPORT_20250526.csv`

#### 🗄️ الهيكل الجديد للجداول:
```sql
-- بدون مفتاح أساسي id
CREATE TABLE `accounting` (
    `request_number` varchar(50),
    `taxpayer_name` varchar(255),
    `office_code` varchar(10),
    `office_name` varchar(255),
    `export_date` date NOT NULL, -- المرجع الأساسي
    -- باقي الحقول...
    KEY `idx_export_date` (`export_date`),
    KEY `idx_request_number` (`request_number`)
);
```

#### 🛠️ الملفات الجديدة:
- `recreate_main_tables.php` - إعادة إنشاء الجداول الأساسية بالهيكل الصحيح

#### 🔄 الملفات المحدثة:
- `includes/functions.php` - إضافة دوال استخراج التاريخ ونوع الجدول
- `index.php` - إضافة رابط إعادة إنشاء الجداول الأساسية
- `README.md` - تحديث التوثيق
- `CHANGELOG.md` - توثيق التغييرات

#### ✅ الفوائد:
- **مطابقة تامة** لمتطلبات النظام
- **تنظيم أفضل** للبيانات حسب تاريخ التصدير
- **استيراد أسرع** بدون قيود المفتاح الأساسي
- **مرونة أكبر** في إدارة البيانات

---

## الإصدار 1.3 - 2025-06-17

### 🎉 الإصدار الكامل مع جميع المميزات المطلوبة

#### 📊 لوحة التحكم التفاعلية
- **مخططات بيانية تفاعلية** باستخدام Chart.js
- **مؤشرات الأداء الرئيسية (KPIs)** في الوقت الفعلي
- **إحصائيات شاملة** لجميع أنواع الطلبات
- **تحليل البيانات** حسب المناطق ومديري العموم
- **واجهة سهلة الاستخدام** مع تأثيرات بصرية جذابة

#### 📈 التقارير المتخصصة
- **تقارير منفصلة** لكل نوع من أنواع الطلبات:
  - تقارير طلبات المحاسبة
  - تقارير طلبات إنهاء النزاع
  - تقارير طلبات تسوية النزاع
- **تصفية متقدمة** حسب:
  - التاريخ (من وإلى)
  - مديري العموم
  - الاختصاص
  - المنطقة
  - المامورية
  - حالة الطلب
  - نوع الوعاء
- **ترقيم الصفحات** للتعامل مع البيانات الكبيرة
- **إحصائيات فورية** لكل تقرير

#### 🔍 نظام البحث المتقدم
- **بحث شامل** في جميع الجداول
- **بحث نصي** في رقم الطلب، اسم المكلف، المأمورية، البريد الإلكتروني، رقم الهاتف
- **فلاتر متقدمة** مع جميع معايير التصفية
- **نتائج مرتبة** حسب التاريخ والأهمية
- **واجهة بحث سهلة** مع معاينة فورية للنتائج

#### 🌙 وضع الليل/النهار
- **تبديل تلقائي** بين الوضع النهاري والليلي
- **حفظ التفضيلات** في التخزين المحلي
- **تصميم متجاوب** مع جميع عناصر الواجهة
- **اختصار لوحة المفاتيح** (Ctrl + Shift + T)
- **تكامل مع تفضيلات النظام**

#### 📑 تصدير PDF متقدم
- **تصدير التقارير إلى PDF** مع تنسيق احترافي
- **تضمين الفلاتر المطبقة** في التقرير
- **إحصائيات مفصلة** في كل تقرير
- **تصميم عربي** مع دعم كامل للنصوص العربية
- **خيارات طباعة** محسنة

### 🛠️ التحسينات التقنية

#### الملفات الجديدة:
- `dashboard.php` - لوحة التحكم الرئيسية
- `assets/js/dashboard.js` - JavaScript للمخططات البيانية
- `search.php` - نظام البحث المتقدم
- `assets/js/theme.js` - إدارة الثيمات
- `includes/pdf_export.php` - نظام تصدير PDF
- `reports/accounting.php` - تقارير المحاسبة
- `reports/conflict.php` - تقارير إنهاء النزاع
- `reports/dispute.php` - تقارير تسوية النزاع

#### الملفات المحدثة:
- `assets/css/main.css` - إضافة أنماط الوضع الليلي
- `index.php` - إضافة روابط المميزات الجديدة
- `includes/functions.php` - دوال محسنة للتقارير

### 🎯 المميزات المكتملة

✅ **لوحة التحكم مع المخططات البيانية**
✅ **صفحات التقارير المنفصلة** مع تصفية شاملة
✅ **نظام البحث المتقدم**
✅ **وضع الليل/النهار**
✅ **تصدير PDF** للتقارير

### 📊 الإحصائيات

- **5 صفحات جديدة** للتقارير والتحليل
- **4 ملفات JavaScript** للتفاعل والمخططات
- **3 أنواع تصدير** (CSV, PDF, طباعة)
- **10+ مخططات بيانية** تفاعلية
- **دعم كامل للغة العربية** في جميع المكونات

---

## الإصدار 1.2 - 2025-06-17

### ✅ إعادة هيكلة الجداول المساعدة

#### 🗄️ تحديث الجداول المرجعية
- **إعادة إنشاء جدول التصنيف**: 7 حقول طبقاً لملف "جدول التصنيف.csv"
  - التصنيف، كود المأمورية، اسم المأمورية، المنطقة، المامورية، الاختصاص، مديري العموم
- **إعادة إنشاء جدول الوعاء**: 3 حقول طبقاً لملف "جدول الوعاء.csv"
  - الوعاء، اسم الوعاء، التصنيف
- **إعادة إنشاء جدول حالة الطلب**: 2 حقل طبقاً لملف "جدول حالة الطلب.csv"
  - حالة الطلب، وصف الحالة

#### 📥 استيراد تلقائي من ملفات CSV
- **قراءة مباشرة** من ملفات Mapping Table
- **تنظيف البيانات** تلقائياً أثناء الاستيراد
- **التحقق من صحة البيانات** قبل الإدراج

#### 🛠️ أدوات إدارة جديدة
- **ملف إعادة الإنشاء**: `recreate_mapping_tables.php`
- **ملف عرض البيانات**: `view_mapping_tables.php`
- **تحديث الدوال المساعدة** في `includes/functions.php`

### 📁 الملفات الجديدة
- `recreate_mapping_tables.php` - إعادة إنشاء الجداول المساعدة
- `view_mapping_tables.php` - عرض محتويات الجداول المساعدة

### 📁 الملفات المحدثة
- `database_setup.php` - تحديث إنشاء الجداول المرجعية
- `includes/functions.php` - إضافة دوال للتعامل مع الجداول الجديدة
- `index.php` - إضافة روابط الأدوات الجديدة
- `README.md` - تحديث التوثيق
- `CHANGELOG.md` - توثيق التغييرات

---

## الإصدار 1.1 - 2025-06-17

### ✅ التحسينات الرئيسية

#### 🗄️ تحسين هيكل قاعدة البيانات
- **إزالة جميع القيود والمفاتيح المنفردة** من الجداول الأساسية (accounting, conflict, dispute)
- **تسهيل الاستيراد المستمر** للبيانات دون تعارضات
- **تحسين الأداء** عند استيراد كميات كبيرة من البيانات

#### 📥 تحسين نظام استيراد البيانات
- **خيار حذف البيانات السابقة**: يمكن اختيار حذف جميع البيانات أو بيانات نفس التاريخ فقط
- **حذف تلقائي للبيانات المكررة**: تجنب تكرار البيانات لنفس تاريخ التصدير
- **تحسين معالجة الأخطاء**: رسائل أوضح وتتبع أفضل للعملية

#### 🗂️ إضافة صفحة إدارة البيانات الجديدة
- **عرض إحصائيات مفصلة** لكل جدول (عدد السجلات، الحجم، التوزيع حسب التاريخ)
- **حذف جدول كامل** مع تأكيد الأمان
- **حذف بيانات تاريخ محدد** لتنظيف البيانات القديمة
- **تحسين الجداول** لتحسين الأداء

### 🔧 التغييرات التقنية

#### قاعدة البيانات:
```sql
-- قبل التحديث
CREATE TABLE accounting (
    id bigint PRIMARY KEY,
    request_number varchar(50) NOT NULL,
    export_date date NOT NULL,
    -- مفاتيح وفهارس متعددة
);

-- بعد التحديث
CREATE TABLE accounting (
    id bigint PRIMARY KEY,
    request_number varchar(50),
    export_date date,
    -- بدون قيود إضافية
);
```

#### استيراد البيانات:
```php
// إضافة خيار حذف البيانات
if ($clearTable) {
    $pdo->exec("DELETE FROM `$table`");
} else {
    $pdo->exec("DELETE FROM `$table` WHERE export_date = ?");
}
```

### 📋 الملفات المحدثة

#### ملفات جديدة:
- `data_management.php` - صفحة إدارة البيانات
- `CHANGELOG.md` - سجل التغييرات

#### ملفات محدثة:
- `database_setup_part2.php` - إزالة القيود من جداول accounting و conflict
- `database_setup_part3.php` - إزالة القيود من جدول dispute وإزالة الفهارس الإضافية
- `import_data.php` - إضافة خيارات الحذف وتحسين المعالجة
- `index.php` - إضافة رابط إدارة البيانات
- `README.md` - تحديث التوثيق

### 🎯 الفوائد

#### للمطورين:
- **سهولة الصيانة**: لا توجد قيود معقدة تعيق التطوير
- **مرونة في التحديث**: يمكن تعديل البيانات دون قيود
- **أداء أفضل**: استيراد أسرع للبيانات الكبيرة

#### للمستخدمين:
- **استيراد أسرع**: لا توجد تأخيرات بسبب فحص القيود
- **مرونة في الإدارة**: خيارات متعددة لحذف وتنظيف البيانات
- **شفافية أكبر**: إحصائيات مفصلة عن البيانات

### ⚠️ ملاحظات مهمة

#### قبل التحديث:
1. **عمل نسخة احتياطية** من قاعدة البيانات الحالية
2. **اختبار النظام** في بيئة تطوير أولاً
3. **التأكد من صحة البيانات** بعد التحديث

#### بعد التحديث:
1. **إعادة إنشاء قاعدة البيانات** باستخدام الملفات المحدثة
2. **إعادة استيراد البيانات** باستخدام النظام الجديد
3. **اختبار جميع الوظائف** للتأكد من عملها

### 🚀 الخطوات التالية

#### الإصدار القادم (1.2):
- [ ] إضافة لوحة التحكم مع المخططات البيانية
- [ ] تطوير صفحات التقارير المنفصلة
- [ ] إضافة نظام البحث المتقدم
- [ ] تطوير وضع الليل/النهار

#### تحسينات مستقبلية:
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] تطوير API للتكامل مع الأنظمة الأخرى
- [ ] إضافة التصدير إلى PDF
- [ ] تحسين الأمان والحماية

---

## الإصدار 1.0 - 2025-06-17

### 🎉 الإطلاق الأولي
- إنشاء النظام الأساسي
- إعداد قاعدة البيانات
- تطوير نظام استيراد البيانات
- تصميم الواجهة العربية
- إنشاء الهيكل التنظيمي للمشروع

---

**ملاحظة**: هذا السجل يوثق جميع التغييرات المهمة في المشروع. للحصول على تفاصيل أكثر، راجع ملفات الكود المصدري.
