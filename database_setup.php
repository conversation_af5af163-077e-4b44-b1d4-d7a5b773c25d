<?php
// database_setup.php
// إعادة تصميم شامل لإنشاء قاعدة البيانات والجداول المرجعية وجداول المعاملات
// المرحلة الأولى: الجداول المرجعية + جدول الطلبات الموحد
// المرحلة الثانية: جداول الاستيراد (Accounting, Dispute, Conflict)

$host = 'localhost';
$user = 'root';
$pass = '';
$dbname = 'tiaf_db';

$conn = new mysqli($host, $user, $pass);
if ($conn->connect_error) {
    die('فشل الاتصال بقاعدة البيانات: ' . $conn->connect_error);
}

// إنشاء قاعدة البيانات إذا لم تكن موجودة
$conn->query("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
$conn->select_db($dbname);

// 1. إنشاء الجداول المرجعية
// تعطيل التحقق من القيود الأجنبية مؤقتاً
$conn->query("SET foreign_key_checks = 0;");
// حذف جدول الطلبات أولاً ثم الجداول المرجعية بالترتيب الصحيح
$conn->query("DROP TABLE IF EXISTS requests");
$conn->query("DROP TABLE IF EXISTS accounting");
$conn->query("DROP TABLE IF EXISTS dispute");
$conn->query("DROP TABLE IF EXISTS conflict");
$conn->query("DROP TABLE IF EXISTS classification");
$conn->query("DROP TABLE IF EXISTS container");
$conn->query("DROP TABLE IF EXISTS request_status");
// إعادة تفعيل التحقق من القيود الأجنبية
$conn->query("SET foreign_key_checks = 1;");

$sql_classification = "CREATE TABLE classification (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$sql_container = "CREATE TABLE container (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$sql_status = "CREATE TABLE request_status (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$conn->query($sql_classification);
$conn->query($sql_container);
$conn->query($sql_status);

// تعبئة الجداول المرجعية ببيانات ثابتة (أمثلة)
$conn->query("INSERT INTO classification (name_ar, name_en) VALUES
    ('تصنيف 1', 'Classification 1'),
    ('تصنيف 2', 'Classification 2')");
$conn->query("INSERT INTO container (name_ar, name_en) VALUES
    ('وعاء 1', 'Container 1'),
    ('وعاء 2', 'Container 2')");
$conn->query("INSERT INTO request_status (name_ar, name_en) VALUES
    ('جديد', 'New'),
    ('مغلق', 'Closed'),
    ('قيد المعالجة', 'In Progress')");

// 2. إنشاء جدول الطلبات الموحد
$sql_requests = "CREATE TABLE requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    office_code VARCHAR(50),
    classification_id INT,
    container_id INT,
    status_id INT,
    request_date DATE,
    amount DECIMAL(18,2),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (classification_id) REFERENCES classification(id),
    FOREIGN KEY (container_id) REFERENCES container(id),
    FOREIGN KEY (status_id) REFERENCES request_status(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
$conn->query($sql_requests);

// 3. إنشاء الفهارس
$conn->query("CREATE INDEX idx_office_code ON requests(office_code)");
$conn->query("CREATE INDEX idx_status_id ON requests(status_id)");
$conn->query("CREATE INDEX idx_container_id ON requests(container_id)");

// المرحلة الثانية: جداول الاستيراد (بدون قيود)
$conn->query("DROP TABLE IF EXISTS accounting");
$conn->query("DROP TABLE IF EXISTS dispute");
$conn->query("DROP TABLE IF EXISTS conflict");

// جدول Accounting
$sql_accounting = "CREATE TABLE accounting (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_number VARCHAR(50),
    status_created_at VARCHAR(50),
    created_by VARCHAR(100),
    modified_by VARCHAR(100),
    modified_at VARCHAR(50),
    status_code VARCHAR(20),
    status_desc VARCHAR(100),
    tax_registration_number VARCHAR(50),
    office_code VARCHAR(20),
    office_name VARCHAR(255),
    taxpayer_name VARCHAR(255),
    address VARCHAR(255),
    email VARCHAR(100),
    phone VARCHAR(50),
    container_code VARCHAR(20),
    container_name VARCHAR(100),
    period VARCHAR(20),
    container_value DECIMAL(18,2),
    shares_cost DECIMAL(18,2),
    container_value_before DECIMAL(18,2),
    shares_cost_before DECIMAL(18,2),
    expected_tax DECIMAL(18,2),
    tax_by_law DECIMAL(18,2),
    Export_Date DATE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

// جدول Dispute
$sql_dispute = "CREATE TABLE dispute (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_number VARCHAR(50),
    request_created_at VARCHAR(50),
    created_by VARCHAR(100),
    modified_at VARCHAR(50),
    modified_by VARCHAR(100),
    request_status_code VARCHAR(20),
    request_status_desc VARCHAR(100),
    request_type_code VARCHAR(20),
    request_type_name VARCHAR(100),
    tax_registration_number VARCHAR(50),
    office_code VARCHAR(20),
    office_name VARCHAR(255),
    taxpayer_name VARCHAR(255),
    address VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(100),
    accounting_type_code VARCHAR(20),
    accounting_type_desc VARCHAR(100),
    container_code VARCHAR(20),
    container_desc VARCHAR(100),
    period VARCHAR(20),
    dispute_stage_code VARCHAR(20),
    dispute_stage_name VARCHAR(100),
    case_number VARCHAR(50),
    dispute_entity_code VARCHAR(20),
    dispute_entity_name VARCHAR(100),
    other_entity VARCHAR(100),
    tax_by_decision DECIMAL(18,2),
    tax_last_assessment DECIMAL(18,2),
    last_assessment_year VARCHAR(20),
    tax_by_model DECIMAL(18,2),
    paid_tax DECIMAL(18,2),
    tax_by_decision_before DECIMAL(18,2),
    tax_last_assessment_before DECIMAL(18,2),
    tax_by_model_before DECIMAL(18,2),
    paid_tax_before DECIMAL(18,2),
    tax_by_law DECIMAL(18,2),
    expected_tax DECIMAL(18,2),
    due_tax DECIMAL(18,2),
    Export_Date DATE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

// جدول Conflict
$sql_conflict = "CREATE TABLE conflict (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_number VARCHAR(50),
    request_created_at VARCHAR(50),
    created_by VARCHAR(100),
    modified_at VARCHAR(50),
    modified_by VARCHAR(100),
    request_status_code VARCHAR(20),
    request_status_desc VARCHAR(100),
    request_type_code VARCHAR(20),
    request_type_name VARCHAR(100),
    tax_registration_number VARCHAR(50),
    office_code VARCHAR(20),
    office_name VARCHAR(255),
    taxpayer_name VARCHAR(255),
    address VARCHAR(255),
    phone VARCHAR(50),
    email VARCHAR(100),
    accounting_type_code VARCHAR(20),
    accounting_type_desc VARCHAR(100),
    container_code VARCHAR(20),
    container_name VARCHAR(100),
    period VARCHAR(20),
    dispute_stage_code VARCHAR(20),
    dispute_stage_desc VARCHAR(100),
    case_number VARCHAR(50),
    dispute_entity_code VARCHAR(20),
    dispute_entity_name VARCHAR(100),
    other_entity VARCHAR(100),
    tax_by_decision DECIMAL(18,2),
    tax_last_assessment DECIMAL(18,2),
    last_assessment_year VARCHAR(20),
    tax_by_model DECIMAL(18,2),
    paid_tax DECIMAL(18,2),
    tax_by_decision_before DECIMAL(18,2),
    tax_last_assessment_before DECIMAL(18,2),
    tax_by_model_before DECIMAL(18,2),
    paid_tax_before DECIMAL(18,2),
    expected_tax DECIMAL(18,2),
    tax_by_law DECIMAL(18,2),
    due_tax DECIMAL(18,2),
    Export_Date DATE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

$conn->query($sql_accounting);
$conn->query($sql_dispute);
$conn->query($sql_conflict);

// ملاحظة: يجب تعديل الحقول في جداول accounting/dispute/conflict لتطابق أعمدة ملفات CSV
// ويمكنك استخدام ملف import.php لاستيراد البيانات مع تعبئة Export_Date تلقائياً من اسم الملف

echo '<div style="background:#e3f2fd;padding:18px;border-radius:8px;direction:rtl;font-family:Tajawal">';
echo 'تم إنشاء قاعدة البيانات والجداول بنجاح.<br>يمكنك الآن استيراد البيانات من خلال صفحة الاستيراد.';
echo '</div>';

$conn->close();
?>
