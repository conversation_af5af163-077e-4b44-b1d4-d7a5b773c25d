<?php
/**
 * ملف إعداد قاعدة البيانات - TIaF Report System
 * إنشاء قاعدة البيانات والجداول المطلوبة
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>🚀 بدء إعداد قاعدة البيانات TIaF Report System</h2>";

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: $database</p>";

    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE `$database`");

    // ===== إنشاء الجداول المرجعية =====

    // جدول التصنيف (Classification)
    $sql_classification = "
    CREATE TABLE IF NOT EXISTS `classification` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(10) NOT NULL,
        `name` varchar(255) NOT NULL,
        `description` text,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_classification);
    echo "<p>✅ تم إنشاء جدول التصنيف (classification)</p>";

    // جدول الوعاء (Container)
    $sql_container = "
    CREATE TABLE IF NOT EXISTS `container` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(10) NOT NULL,
        `name` varchar(255) NOT NULL,
        `description` text,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_container);
    echo "<p>✅ تم إنشاء جدول الوعاء (container)</p>";

    // جدول حالة الطلب (Request Status)
    $sql_request_status = "
    CREATE TABLE IF NOT EXISTS `request_status` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `code` varchar(10) NOT NULL,
        `name` varchar(255) NOT NULL,
        `description` text,
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_request_status);
    echo "<p>✅ تم إنشاء جدول حالة الطلب (request_status)</p>";

    // ===== إدخال البيانات المرجعية =====

    // بيانات التصنيف
    $classification_data = [
        ['1', 'تقديري'],
        ['2', 'حسابات']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO classification (code, name) VALUES (?, ?)");
    foreach ($classification_data as $data) {
        $stmt->execute($data);
    }
    echo "<p>✅ تم إدخال بيانات التصنيف</p>";

    // بيانات الوعاء
    $container_data = [
        ['1', 'الدخل'],
        ['2', 'القيمة المضافة'],
        ['3', 'المرتبات'],
        ['4', 'الدمغة'],
        ['7', 'تصرفات عقارية']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO container (code, name) VALUES (?, ?)");
    foreach ($container_data as $data) {
        $stmt->execute($data);
    }
    echo "<p>✅ تم إدخال بيانات الوعاء</p>";

    // بيانات حالة الطلب
    $status_data = [
        ['20', 'قيد المراجعة'],
        ['21', 'مطلوب التوجه للمأمورية / اللجنة المختصة'],
        ['22', 'جاري نظر النزاع'],
        ['30', 'قيد الاعتماد'],
        ['40', 'تم قبول الطلب'],
        ['50', 'تم رفض الطلب']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO request_status (code, name) VALUES (?, ?)");
    foreach ($status_data as $data) {
        $stmt->execute($data);
    }
    echo "<p>✅ تم إدخال بيانات حالة الطلب</p>";

    echo "<h3>المرحلة الأولى مكتملة - الجداول المرجعية</h3>";
    echo "<p><a href='database_setup_part2.php'>انقر هنا لإكمال إنشاء الجداول الأساسية</a></p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
}
?>