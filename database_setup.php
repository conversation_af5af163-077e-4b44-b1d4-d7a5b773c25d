<?php
/**
 * ملف إعداد قاعدة البيانات - TIaF Report System
 * إنشاء قاعدة البيانات والجداول المطلوبة
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

try {
    // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "<h2>🚀 بدء إعداد قاعدة البيانات TIaF Report System</h2>";

    // إنشاء قاعدة البيانات
    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "<p>✅ تم إنشاء قاعدة البيانات: $database</p>";

    // الاتصال بقاعدة البيانات المحددة
    $pdo->exec("USE `$database`");

    // ===== إنشاء الجداول المرجعية =====

    // جدول التصنيف (Classification) - 7 حقول كما هو في ملف CSV
    $sql_classification = "
    CREATE TABLE IF NOT EXISTS `classification` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `classification_type` varchar(50) NOT NULL COMMENT 'التصنيف',
        `office_code` varchar(10) NOT NULL COMMENT 'كود المأمورية',
        `office_name` varchar(255) NOT NULL COMMENT 'اسم المأمورية',
        `region` varchar(100) NOT NULL COMMENT 'المنطقة',
        `office_branch` varchar(255) NOT NULL COMMENT 'المامورية',
        `specialization` varchar(100) NOT NULL COMMENT 'الاختصاص',
        `general_manager` varchar(100) NOT NULL COMMENT 'مديري العموم',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_office_code` (`office_code`),
        KEY `idx_classification_type` (`classification_type`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_classification);
    echo "<p>✅ تم إنشاء جدول التصنيف (classification) - 7 حقول</p>";

    // جدول الوعاء (Container) - 3 حقول كما هو في ملف CSV
    $sql_container = "
    CREATE TABLE IF NOT EXISTS `container` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `container_code` varchar(10) NOT NULL COMMENT 'الوعاء',
        `container_name` varchar(255) NOT NULL COMMENT 'اسم الوعاء',
        `classification` varchar(50) NOT NULL COMMENT 'التصنيف',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_container_code` (`container_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_container);
    echo "<p>✅ تم إنشاء جدول الوعاء (container) - 3 حقول</p>";

    // جدول حالة الطلب (Request Status) - 2 حقل كما هو في ملف CSV
    $sql_request_status = "
    CREATE TABLE IF NOT EXISTS `request_status` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `status_code` varchar(10) NOT NULL COMMENT 'حالة الطلب',
        `status_description` varchar(255) NOT NULL COMMENT 'وصف الحالة',
        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        KEY `idx_status_code` (`status_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    $pdo->exec($sql_request_status);
    echo "<p>✅ تم إنشاء جدول حالة الطلب (request_status) - 2 حقل</p>";

    // ===== إدخال البيانات المرجعية من ملفات CSV =====

    echo "<h3>📥 استيراد البيانات من ملفات Mapping Table</h3>";

    // استيراد بيانات التصنيف من ملف CSV
    $classification_file = 'Mapping Table/جدول التصنيف.csv';
    if (file_exists($classification_file)) {
        $handle = fopen($classification_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO classification (classification_type, office_code, office_name, region, office_branch, specialization, general_manager) VALUES (?, ?, ?, ?, ?, ?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 7) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات التصنيف</p>";
    } else {
        echo "<p>⚠️ ملف التصنيف غير موجود</p>";
    }

    // استيراد بيانات الوعاء من ملف CSV
    $container_file = 'Mapping Table/جدول الوعاء.csv';
    if (file_exists($container_file)) {
        $handle = fopen($container_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO container (container_code, container_name, classification) VALUES (?, ?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 3) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات الوعاء</p>";
    } else {
        echo "<p>⚠️ ملف الوعاء غير موجود</p>";
    }

    // استيراد بيانات حالة الطلب من ملف CSV
    $status_file = 'Mapping Table/جدول حالة الطلب.csv';
    if (file_exists($status_file)) {
        $handle = fopen($status_file, 'r');
        $headers = fgetcsv($handle); // تخطي العنوان الأول

        $stmt = $pdo->prepare("INSERT IGNORE INTO request_status (status_code, status_description) VALUES (?, ?)");

        $count = 0;
        while (($data = fgetcsv($handle)) !== FALSE) {
            if (count($data) >= 2) {
                $stmt->execute($data);
                $count++;
            }
        }
        fclose($handle);
        echo "<p>✅ تم إدخال $count سجل من بيانات حالة الطلب</p>";
    } else {
        echo "<p>⚠️ ملف حالة الطلب غير موجود</p>";
    }

    echo "<h3>المرحلة الأولى مكتملة - الجداول المرجعية</h3>";
    echo "<p><a href='database_setup_part2.php'>انقر هنا لإكمال إنشاء الجداول الأساسية</a></p>";

} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</p>";
    die();
}
?>