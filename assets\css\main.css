/**
 * الأنماط الرئيسية - TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

/* استيراد الخطوط العربية */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Cairo:wght@300;400;500;700&display=swap');

/* إعادة تعيين الأنماط الافتراضية */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* الأنماط الأساسية */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #2c3e50;
    --gray-color: #6c757d;
    
    --font-family-arabic: 'Tajawal', 'Cairo', Arial, sans-serif;
    --font-family-latin: '<PERSON><PERSON> Sans', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    
    --border-radius: 8px;
    --border-radius-lg: 15px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --box-shadow-lg: 0 10px 30px rgba(0,0,0,0.15);
    
    --transition: all 0.3s ease;
}

body {
    font-family: var(--font-family-arabic);
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
}

/* الحاويات */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.container-fluid {
    width: 100%;
    padding: 0 15px;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* البطاقات */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
    overflow: hidden;
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--dark-color) 0%, #34495e 100%);
    color: white;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.card-body {
    padding: 20px;
}

.card-footer {
    background: var(--light-color);
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 25px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    text-align: center;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-family: inherit;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    text-decoration: none;
    color: white;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #fd7e14 100%);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, #fd7e14 100%);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color) 0%, #6f42c1 100%);
}

.btn-light {
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid #dee2e6;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.125rem;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 16px 12px;
    padding-left: 40px;
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.table-striped tbody tr:nth-child(odd) {
    background: #f8f9fa;
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border-color: #c3e6cb;
}

.alert-warning {
    background: #fff3cd;
    color: #856404;
    border-color: #ffeaa7;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border-color: #f5c6cb;
}

.alert-info {
    background: #d1ecf1;
    color: #0c5460;
    border-color: #bee5eb;
}

/* شريط التقدم */
.progress {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
    height: 100%;
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

/* الأدوات المساعدة */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }
.p-5 { padding: 3rem; }

/* الوضع الليلي */
.dark-mode {
    --light-color: #2c3e50;
    --dark-color: #ecf0f1;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: var(--dark-color);
}

.dark-mode .card {
    background: #34495e;
    color: var(--dark-color);
}

.dark-mode .table {
    background: #34495e;
    color: var(--dark-color);
}

.dark-mode .table th {
    background: #2c3e50;
}

.dark-mode .form-control {
    background: #34495e;
    color: var(--dark-color);
    border-color: #4a5f7a;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .row {
        margin: 0 -10px;
    }
    
    .col {
        padding: 0 10px;
    }
    
    .col-sm-12 {
        flex: 0 0 100%;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .card-body {
        padding: 15px;
    }
}

@media (max-width: 576px) {
    .table {
        font-size: 0.75rem;
    }
    
    .table th,
    .table td {
        padding: 8px 10px;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.875rem;
    }
}
