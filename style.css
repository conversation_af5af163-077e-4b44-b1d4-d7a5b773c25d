@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;700&family=Fira+Sans:wght@400;700&display=swap');
:root {
  --main-bg: #f9f9f9;
  --main-color: #1a237e;
  --card-bg: #fff;
  --kpi-bg: #e3e6f3;
  --kpi-green: #a5d6a7;
  --kpi-purple: #b39ddb;
  --kpi-gray: #ececec;
  --border: #bbb;
  --transition: 0.3s;
}
body {
  font-family: 'Ta<PERSON>wal', 'Fira Sans', Arial, sans-serif;
  background: var(--main-bg);
  color: var(--main-color);
  margin: 0;
  padding: 0;
  direction: rtl;
  transition: background var(--transition), color var(--transition);
}
.dashboard-container {
  max-width: 1200px;
  margin: 40px auto;
  background: var(--card-bg);
  border-radius: 16px;
  box-shadow: 0 2px 12px #ccc;
  padding: 32px;
}
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}
h1 {
  font-size: 2.2rem;
  color: var(--main-color);
}
#toggle-mode {
  background: var(--kpi-purple);
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 10px 24px;
  font-size: 1rem;
  cursor: pointer;
  transition: background var(--transition);
}
#toggle-mode:hover {
  background: var(--main-color);
}
.kpi-cards {
  display: flex;
  gap: 24px;
  margin-bottom: 32px;
}
.kpi-card {
  flex: 1;
  background: var(--kpi-bg);
  border-radius: 12px;
  padding: 24px 0;
  text-align: center;
  box-shadow: 0 1px 4px #eee;
  transition: background var(--transition);
}
.kpi-card h2 {
  font-size: 1.2rem;
  margin-bottom: 12px;
}
.kpi-card span {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--main-color);
}
.charts-section {
  display: flex;
  gap: 32px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}
.charts-section canvas {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 1px 4px #eee;
  padding: 16px;
  flex: 1 1 350px;
  min-width: 300px;
  max-width: 500px;
}
.data-table-section {
  margin-top: 32px;
}
#data-table-container {
  overflow-x: auto;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 1px 4px #eee;
  padding: 16px;
}
.import-container #loadingMsg {
  display: none;
  background: #ede7f6;
  color: #7c4dff;
  font-weight: bold;
  padding: 12px 0;
  border-radius: 8px;
  text-align: center;
  margin: 18px 0;
  font-size: 1.1rem;
}
.import-container #resultMsg .msg {
  background: #f1f8e9;
  color: #388e3c;
  border-radius: 8px;
  padding: 10px 0;
  margin: 12px 0;
  text-align: center;
  font-size: 1.1rem;
}
.import-container #resultMsg .msg[style*="red"] {
  background: #ffebee;
  color: #c62828;
}
/* الوضع الليلي */
body.dark {
  --main-bg: #23232b;
  --main-color: #fff;
  --card-bg: #2d2d3a;
  --kpi-bg: #39395a;
  --kpi-purple: #7c4dff;
  --kpi-green: #388e3c;
  --kpi-gray: #444;
  --border: #444;
}
