<?php
/**
 * صفحة استيراد البيانات من ملفات CSV
 * TIaF Report System
 * 
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

// إعدادات قاعدة البيانات
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'tiaf_db';

// دالة لاستخراج التاريخ من اسم الملف
function extractDateFromFilename($filename) {
    // البحث عن نمط التاريخ في اسم الملف (مثل: EXPORT_20250526)
    if (preg_match('/EXPORT_(\d{8})/', $filename, $matches)) {
        $dateString = $matches[1];
        // تحويل من YYYYMMDD إلى YYYY-MM-DD
        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);
        return "$year-$month-$day";
    }
    return date('Y-m-d'); // التاريخ الحالي كافتراضي
}

// دالة لتنظيف البيانات
function cleanData($data) {
    // إزالة الفواصل من الأرقام
    $data = str_replace(',', '', $data);
    // إزالة المسافات الزائدة
    $data = trim($data);
    return $data;
}

// دالة لتحويل التاريخ من تنسيق DD.MM.YYYY HH:MM:SS إلى YYYY-MM-DD HH:MM:SS
function convertDateTime($dateTime) {
    if (empty($dateTime)) return null;
    
    // تحويل التاريخ من DD.MM.YYYY HH:MM:SS إلى YYYY-MM-DD HH:MM:SS
    if (preg_match('/(\d{2})\.(\d{2})\.(\d{4})\s+(\d{2}:\d{2}:\d{2})/', $dateTime, $matches)) {
        return $matches[3] . '-' . $matches[2] . '-' . $matches[1] . ' ' . $matches[4];
    }
    
    return $dateTime;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد البيانات - TIaF Report System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 40px;
        }
        
        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
        }
        
        .upload-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .file-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            margin-bottom: 15px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        
        .progress-bar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            height: 100%;
            transition: width 0.3s ease;
        }
        
        .table-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .table-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 5px solid #667eea;
        }
        
        .table-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .table-card p {
            color: #6c757d;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗂️ استيراد البيانات</h1>
            <p>نظام تقارير الضرائب والرسوم</p>
        </div>
        
        <div class="content">
            <?php
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['import_table'])) {
                $table = $_POST['import_table'];
                $uploadedFile = $_FILES['csv_file'];
                
                if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
                    try {
                        $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        $filename = $uploadedFile['name'];
                        $exportDate = extractDateFromFilename($filename);
                        
                        echo "<div class='alert alert-info'>";
                        echo "<h4>🚀 بدء استيراد البيانات...</h4>";
                        echo "<p>الملف: $filename</p>";
                        echo "<p>الجدول: $table</p>";
                        echo "<p>تاريخ التصدير: $exportDate</p>";
                        echo "</div>";
                        
                        // قراءة ملف CSV
                        $handle = fopen($uploadedFile['tmp_name'], 'r');
                        if ($handle !== FALSE) {
                            // قراءة العنوان الأول (أسماء الأعمدة)
                            $headers = fgetcsv($handle);
                            
                            // تنظيف أسماء الأعمدة
                            $cleanHeaders = array_map('trim', $headers);
                            
                            $rowCount = 0;
                            $successCount = 0;
                            $errorCount = 0;
                            
                            echo "<div class='progress'><div class='progress-bar' style='width: 0%'></div></div>";
                            echo "<div id='progress-text'>جاري المعالجة...</div>";
                            
                            // إعداد الاستعلام حسب الجدول
                            $sql = prepareInsertSQL($table, $cleanHeaders);
                            $stmt = $pdo->prepare($sql);
                            
                            while (($data = fgetcsv($handle)) !== FALSE) {
                                $rowCount++;
                                
                                try {
                                    // تنظيف البيانات
                                    $cleanData = array_map('cleanData', $data);
                                    
                                    // إضافة تاريخ التصدير
                                    $cleanData[] = $exportDate;
                                    
                                    // تحويل التواريخ
                                    if (isset($cleanData[1])) $cleanData[1] = convertDateTime($cleanData[1]);
                                    if (isset($cleanData[3])) $cleanData[3] = convertDateTime($cleanData[3]);
                                    
                                    $stmt->execute($cleanData);
                                    $successCount++;
                                    
                                } catch (Exception $e) {
                                    $errorCount++;
                                    echo "<p style='color: red;'>خطأ في السطر $rowCount: " . $e->getMessage() . "</p>";
                                }
                                
                                // تحديث شريط التقدم كل 100 سجل
                                if ($rowCount % 100 === 0) {
                                    echo "<script>
                                        document.querySelector('.progress-bar').style.width = '" . min(100, ($rowCount / 1000) * 100) . "%';
                                        document.getElementById('progress-text').textContent = 'تم معالجة $rowCount سجل...';
                                    </script>";
                                    flush();
                                }
                            }
                            
                            fclose($handle);
                            
                            echo "<div class='alert alert-success'>";
                            echo "<h4>✅ تم الانتهاء من الاستيراد!</h4>";
                            echo "<p>إجمالي السجلات: $rowCount</p>";
                            echo "<p>تم بنجاح: $successCount</p>";
                            echo "<p>أخطاء: $errorCount</p>";
                            echo "</div>";
                            
                        } else {
                            echo "<div class='alert alert-error'>❌ خطأ في قراءة الملف</div>";
                        }
                        
                    } catch (Exception $e) {
                        echo "<div class='alert alert-error'>❌ خطأ: " . $e->getMessage() . "</div>";
                    }
                } else {
                    echo "<div class='alert alert-error'>❌ خطأ في رفع الملف</div>";
                }
            }
            
            // دالة لإعداد استعلام الإدراج
            function prepareInsertSQL($table, $headers) {
                $columns = implode('`, `', $headers);
                $placeholders = str_repeat('?,', count($headers)) . '?'; // إضافة placeholder لتاريخ التصدير
                $placeholders = rtrim($placeholders, ',');
                
                return "INSERT INTO `$table` (`$columns`, `export_date`) VALUES ($placeholders)";
            }
            ?>
            
            <div class="table-info">
                <div class="table-card">
                    <h4>📊 طلبات المحاسبة (Accounting)</h4>
                    <p>استيراد بيانات طلبات المحاسبة من ملفات CSV بتنسيق AccountingEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="accounting">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <button type="submit" class="btn btn-success">استيراد بيانات المحاسبة</button>
                    </form>
                </div>
                
                <div class="table-card">
                    <h4>⚖️ طلبات إنهاء النزاع (Conflict)</h4>
                    <p>استيراد بيانات طلبات إنهاء النزاع من ملفات CSV بتنسيق ConflictEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="conflict">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <button type="submit" class="btn btn-success">استيراد بيانات إنهاء النزاع</button>
                    </form>
                </div>
                
                <div class="table-card">
                    <h4>🤝 طلبات تسوية النزاع (Dispute)</h4>
                    <p>استيراد بيانات طلبات تسوية النزاع من ملفات CSV بتنسيق DisputeEXPORT_YYYYMMDD.csv</p>
                    <form method="post" enctype="multipart/form-data" style="margin-top: 15px;">
                        <input type="hidden" name="import_table" value="dispute">
                        <input type="file" name="csv_file" accept=".csv" class="file-input" required>
                        <button type="submit" class="btn btn-success">استيراد بيانات تسوية النزاع</button>
                    </form>
                </div>
            </div>
            
            <div style="text-align: center; margin-top: 40px;">
                <a href="index.php" class="btn">العودة للصفحة الرئيسية</a>
                <a href="dashboard.php" class="btn btn-success">عرض لوحة التحكم</a>
            </div>
        </div>
    </div>
</body>
</html>
