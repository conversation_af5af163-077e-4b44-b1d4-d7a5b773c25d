<?php
/**
 * الصفحة الرئيسية - TIaF Report System
 *
 * <AUTHOR> Development Team
 * @version 1.0
 * @date 2025-06-17
 */

require_once 'config/database.php';
require_once 'includes/functions.php';

// التحقق من وجود قاعدة البيانات
try {
    $pdo = getDBConnection();
    $stmt = $pdo->query("SHOW TABLES LIKE 'accounting'");
    $dbExists = $stmt->rowCount() > 0;
} catch (Exception $e) {
    $dbExists = false;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - الصفحة الرئيسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .nav-section {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .nav-section h2 {
            font-size: 2rem;
            margin-bottom: 20px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            padding: 40px;
        }

        .nav-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid #dee2e6;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .nav-card .icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .nav-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }

        .nav-card p {
            color: #6c757d;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 12px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #6c757d;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .nav-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .nav-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ <?php echo APP_NAME; ?></h1>
            <p>نظام تقارير منظومة التيسيرات الضريبية - الإصدار <?php echo APP_VERSION; ?></p>
        </div>

        <div class="main-content">
            <div class="nav-section">
                <h2>مرحباً بك في نظام التقارير</h2>
                <p>نظام متكامل لإدارة وعرض تقارير طلبات المحاسبة وتسوية وإنهاء النزاعات الضريبية</p>
            </div>

            <?php if (!$dbExists): ?>
            <div style="padding: 30px;">
                <div class="alert alert-warning">
                    <h4>⚠️ يجب إعداد قاعدة البيانات أولاً</h4>
                    <p>لم يتم العثور على قاعدة البيانات. يرجى إعداد قاعدة البيانات قبل المتابعة.</p>
                </div>

                <div class="nav-grid">
                    <div class="nav-card">
                        <span class="icon">🗄️</span>
                        <h3>إعداد قاعدة البيانات</h3>
                        <p>إنشاء قاعدة البيانات والجداول المطلوبة للنظام</p>
                        <a href="database_setup.php" class="btn btn-warning">بدء الإعداد</a>
                    </div>

                <div class="nav-card">
                    <span class="icon">🔄</span>
                    <h3>إعادة إنشاء الجداول المساعدة</h3>
                    <p>إعادة إنشاء الجداول المرجعية طبقاً لملفات Mapping Table</p>
                    <a href="recreate_mapping_tables.php" class="btn btn-info">إعادة الإنشاء</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🗄️</span>
                    <h3>إعادة إنشاء الجداول الأساسية</h3>
                    <p>إعادة إنشاء الجداول الأساسية بدون مفتاح أساسي (منظمة حسب export_date)</p>
                    <a href="recreate_main_tables.php" class="btn btn-warning">إعادة الإنشاء</a>
                </div>

                <div class="nav-card">
                    <span class="icon">📋</span>
                    <h3>عرض الجداول المساعدة</h3>
                    <p>عرض محتويات الجداول المرجعية والتحقق من البيانات</p>
                    <a href="view_mapping_tables.php" class="btn">عرض الجداول</a>
                </div>

                <div class="nav-card">
                    <span class="icon">📊</span>
                    <h3>لوحة التحكم</h3>
                    <p>نظرة شاملة على إحصائيات النظام والمخططات البيانية</p>
                    <a href="dashboard.php" class="btn btn-info">لوحة التحكم</a>
                </div>
                </div>
            </div>
            <?php else: ?>

            <div class="nav-grid">
                <div class="nav-card">
                    <span class="icon">📊</span>
                    <h3>لوحة التحكم</h3>
                    <p>عرض الإحصائيات والمؤشرات الرئيسية للنظام</p>
                    <a href="dashboard.php" class="btn">عرض لوحة التحكم</a>
                </div>

                <div class="nav-card">
                    <span class="icon">📥</span>
                    <h3>استيراد البيانات</h3>
                    <p>رفع وتحديث بيانات الجداول من ملفات CSV</p>
                    <div style="margin-top: 15px;">
                        <a href="import_data_exact_match.php" class="btn btn-success" style="margin: 5px;">📥 استيراد مطابق</a>
                        <a href="import_data.php" class="btn btn-sm" style="margin: 5px;">📊 استيراد عادي</a>
                    </div>
                </div>

                <div class="nav-card">
                    <span class="icon">📈</span>
                    <h3>تقارير المحاسبة</h3>
                    <p>عرض وتحليل بيانات طلبات المحاسبة</p>
                    <a href="reports/accounting.php" class="btn btn-info">عرض التقارير</a>
                </div>

                <div class="nav-card">
                    <span class="icon">⚖️</span>
                    <h3>تقارير إنهاء النزاع</h3>
                    <p>عرض وتحليل بيانات طلبات إنهاء النزاع</p>
                    <a href="reports/conflict.php" class="btn btn-info">عرض التقارير</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🤝</span>
                    <h3>تقارير تسوية النزاع</h3>
                    <p>عرض وتحليل بيانات طلبات تسوية النزاع</p>
                    <a href="reports/dispute.php" class="btn btn-info">عرض التقارير</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🔍</span>
                    <h3>البحث المتقدم</h3>
                    <p>البحث في جميع البيانات بمعايير متقدمة</p>
                    <a href="search.php" class="btn">البحث المتقدم</a>
                </div>

                <div class="nav-card">
                    <span class="icon">📈</span>
                    <h3>التقارير المتخصصة</h3>
                    <p>تقارير مفصلة لكل نوع من أنواع الطلبات</p>
                    <div style="margin-top: 15px;">
                        <a href="reports/accounting.php" class="btn btn-sm" style="margin: 5px;">📊 المحاسبة</a>
                        <a href="reports/conflict.php" class="btn btn-sm" style="margin: 5px;">⚖️ إنهاء النزاع</a>
                        <a href="reports/dispute.php" class="btn btn-sm" style="margin: 5px;">🤝 تسوية النزاع</a>
                    </div>
                </div>

                <div class="nav-card">
                    <span class="icon">🗂️</span>
                    <h3>إدارة البيانات</h3>
                    <p>إدارة وصيانة البيانات، حذف البيانات القديمة</p>
                    <a href="data_management.php" class="btn btn-warning">إدارة البيانات</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🔄</span>
                    <h3>إعادة إنشاء الجداول</h3>
                    <p>إنشاء الجداول الأساسية بأسماء الحقول المطابقة تماماً لأعمدة CSV</p>
                    <div style="margin-top: 15px;">
                        <a href="add_missing_columns.php" class="btn btn-success" style="margin: 5px;">➕ إضافة أعمدة</a>
                        <a href="recreate_tables_with_mapping.php" class="btn btn-danger" style="margin: 5px;">🎯 إنشاء بالتحويل</a>
                        <a href="recreate_tables_from_csv.php" class="btn btn-info" style="margin: 5px;">🔄 إنشاء من CSV</a>
                    </div>
                </div>

                <div class="nav-card">
                    <span class="icon">🧪</span>
                    <h3>اختبار النظام</h3>
                    <p>فحص شامل لجميع مكونات النظام والتأكد من عمله بشكل صحيح</p>
                    <a href="test_system.php" class="btn btn-info">اختبار النظام</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🔍</span>
                    <h3>تشخيص مشاكل الاستيراد</h3>
                    <p>أداة لتشخيص وحل مشاكل استيراد ملفات CSV وتحويل أسماء الأعمدة</p>
                    <a href="debug_import.php" class="btn btn-warning">تشخيص الاستيراد</a>
                </div>

                <div class="nav-card">
                    <span class="icon">🔧</span>
                    <h3>إصلاح ملفات CSV</h3>
                    <p>أداة لإزالة BOM وتنظيف أسماء الأعمدة في ملفات CSV قبل الاستيراد</p>
                    <div style="margin-top: 15px;">
                        <a href="fix_csv_advanced.php" class="btn btn-danger" style="margin: 5px;">🔧 إصلاح متقدم</a>
                        <a href="fix_csv_simple.php" class="btn btn-sm" style="margin: 5px;">📝 إصلاح بسيط</a>
                    </div>
                </div>
            </div>

            <?php endif; ?>

            <div class="footer">
                <p>&copy; 2025 TIaF Report System - جميع الحقوق محفوظة</p>
                <p>تم التطوير بواسطة فريق TIaF Development Team</p>
            </div>
        </div>
    </div>

    <script src="assets/js/theme.js"></script>
</body>
</html>
