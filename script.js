// script.js
// التبديل بين الوضع الليلي والفاتح
window.addEventListener('DOMContentLoaded', function() {
  const toggleBtn = document.getElementById('toggle-mode');
  if (toggleBtn) {
    toggleBtn.onclick = function() {
      document.body.classList.toggle('dark');
    };
  }

  // بيانات وهمية للعرض التجريبي (يجب الربط بقاعدة البيانات لاحقاً)
  const kpi1 = document.getElementById('kpi-accounting');
  const kpi2 = document.getElementById('kpi-dispute');
  const kpi3 = document.getElementById('kpi-conflict');
  if (kpi1) kpi1.textContent = 120;
  if (kpi2) kpi2.textContent = 45;
  if (kpi3) kpi3.textContent = 33;

  // رسم مخطط بياني بالأعمدة
  const barChart = document.getElementById('barChart');
  if (barChart) {
    const barCtx = barChart.getContext('2d');
    new Chart(barCtx, {
      type: 'bar',
      data: {
        labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
        datasets: [{
          label: 'طلبات المحاسبة',
          data: [20, 30, 25, 40, 35, 50],
          backgroundColor: '#7c4dff',
        }, {
          label: 'طلبات تسوية النزاع',
          data: [10, 15, 12, 18, 20, 25],
          backgroundColor: '#388e3c',
        }]
      },
      options: {
        responsive: true,
        plugins: { legend: { position: 'top' } }
      }
    });
  }

  // رسم مخطط دائري
  const pieChart = document.getElementById('pieChart');
  if (pieChart) {
    const pieCtx = pieChart.getContext('2d');
    new Chart(pieCtx, {
      type: 'pie',
      data: {
        labels: ['محاسبة', 'تسوية نزاع', 'انهاء نزاع'],
        datasets: [{
          data: [120, 45, 33],
          backgroundColor: ['#7c4dff', '#388e3c', '#b39ddb']
        }]
      },
      options: { responsive: true }
    });
  }

  // جدول بيانات إحصائي تجريبي
  const tableContainer = document.getElementById('data-table-container');
  if (tableContainer) {
    const tableHTML = `
    <table>
      <thead><tr><th>النوع</th><th>عدد الطلبات</th><th>أحدث تاريخ تصدير</th></tr></thead>
      <tbody>
        <tr><td>محاسبة</td><td>120</td><td>17-06-2025</td></tr>
        <tr><td>تسوية نزاع</td><td>45</td><td>17-06-2025</td></tr>
        <tr><td>انهاء نزاع</td><td>33</td><td>17-06-2025</td></tr>
      </tbody>
    </table>`;
    tableContainer.innerHTML = tableHTML;
  }

  // معالجة استيراد CSV عبر AJAX
  const importForm = document.getElementById('importForm');
  if (importForm) {
    importForm.addEventListener('submit', function(e) {
      e.preventDefault();
      const loadingMsg = document.getElementById('loadingMsg');
      const resultMsg = document.getElementById('resultMsg');
      loadingMsg.style.display = 'block';
      resultMsg.innerHTML = '';
      const formData = new FormData(importForm);
      fetch('import.php', {
        method: 'POST',
        body: formData
      })
      .then(res => res.json())
      .then(data => {
        loadingMsg.style.display = 'none';
        if (data.success) {
          resultMsg.innerHTML = `<div class="msg" style="color:green">${data.message}</div>`;
        } else {
          resultMsg.innerHTML = `<div class="msg" style="color:red">${data.message}</div>`;
        }
      })
      .catch(() => {
        loadingMsg.style.display = 'none';
        resultMsg.innerHTML = '<div class="msg" style="color:red">حدث خطأ أثناء الاستيراد</div>';
      });
    });
  }
});
